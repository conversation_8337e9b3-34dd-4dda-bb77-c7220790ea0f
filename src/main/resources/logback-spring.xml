<?xml version="1.0" encoding="UTF-8"?>
<!-- 参考：https://juejin.cn/post/7296297918143184935 -->
<!-- scan 配置文件如果发生改变，将会被重新加载  scanPeriod 检测间隔时间  scan="true" scanPeriod="60 seconds" debug="false"-->
<!--<configuration scan="true" scanPeriod="60 seconds" debug="false">-->
<configuration debug="false">
    <contextName>spring-boot-log</contextName>

    <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
    <property name="FILE_LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger Line:%-3L - %msg%n"/>
    <!-- 控制台格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="%cyan(%d{yyyy-MM-dd HH:mm:ss}) %green([%thread]) %highlight(%-5level) %boldMagenta(%logger{36}) Line:%-3L - %msg%n"/>
    <!-- 带traceId和userId -->
    <!-- <property name="FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId:-}] [%X{userId:-}] %logger Line:%-3L - %msg%n"/>-->
    <!-- 其他格式 -->
    <!-- <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger Line:%-3L - %msg%n</pattern> -->
    <!-- <pattern>%d{HH:mm:ss.SSS} -%5p [%X{SessionId}] %-30.30logger{10} : %m%n</pattern> -->
    <!-- 阿里的console log pattern -->
    <!-- <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />-->
    <!-- <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter" />-->
    <!-- <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />-->
    <!-- <property name="CONSOLE_LOG_PATTERN" value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(-&#45;&#45;){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}" />-->


    <!-- 读取 Spring Boot 中的配置变量 -->
    <springProperty name="LOG_PATH" source="logging.file.path" defaultValue="./logs"/>
    <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
    <property name="ASYNC_QUEUE_SIZE" value="512"/>
    <!-- 默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
    <property name="ASYNC_DISCARDING_THRESHOLD" value="0"/>

    <!-- 控制台 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <!-- 日志格式 -->
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <!-- TRACE日志 -->
    <appender name="TRACE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/trace.log</file>
        <append>true</append>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/trace-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 文件大小限制 -->
            <maxFileSize>10MB</maxFileSize>
            <!-- 最大保存时间：15天-->
            <maxHistory>15</maxHistory>
            <!-- 总文件大小-->
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <!-- 日志格式 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!-- 日志级别过滤器 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 匹配时的操作：接收（记录） -->
            <level>TRACE</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!-- 异步输出，提升性能 -->
    <appender name="ASYNC_TRACE_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>${ASYNC_QUEUE_SIZE}</queueSize>
        <!-- 队列满时丢弃日志，不阻塞主线程 -->
        <neverBlock>true</neverBlock>
        <!-- 默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>${ASYNC_DISCARDING_THRESHOLD}</discardingThreshold>
        <appender-ref ref="TRACE_FILE"/>
    </appender>

    <!-- DEBUG日志 -->
    <appender name="DEBUG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/debug.log</file>
        <append>true</append>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/debug-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 文件大小限制 -->
            <maxFileSize>10MB</maxFileSize>
            <!-- 最大保存时间：15天-->
            <maxHistory>15</maxHistory>
            <!-- 总文件大小-->
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <!-- 日志格式 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!-- 日志级别过滤器 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 匹配时的操作：接收（记录） -->
            <level>DEBUG</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!-- 异步输出，提升性能 -->
    <appender name="ASYNC_DEBUG_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>${ASYNC_QUEUE_SIZE}</queueSize>
        <!-- 队列满时丢弃日志，不阻塞主线程 -->
        <neverBlock>true</neverBlock>
        <!-- 默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>${ASYNC_DISCARDING_THRESHOLD}</discardingThreshold>
        <appender-ref ref="DEBUG_FILE"/>
    </appender>

    <!-- INFO日志 -->
    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/info.log</file>
        <append>true</append>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/info-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 文件大小限制 -->
            <maxFileSize>10MB</maxFileSize>
            <!-- 最大保存时间：180天-->
            <maxHistory>180</maxHistory>
            <!-- 总文件大小-->
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <!-- 日志格式 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!-- 日志级别过滤器 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 匹配时的操作：接收（记录） -->
            <level>INFO</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!-- 异步输出，提升性能 -->
    <appender name="ASYNC_INFO_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>${ASYNC_QUEUE_SIZE}</queueSize>
        <!-- 队列满时丢弃日志，不阻塞主线程 -->
        <neverBlock>true</neverBlock>
        <!-- 默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>${ASYNC_DISCARDING_THRESHOLD}</discardingThreshold>
        <appender-ref ref="INFO_FILE"/>
    </appender>

    <!-- WARN日志 -->
    <appender name="WARN_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/warn.log</file>
        <append>true</append>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/warn-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 文件大小限制 -->
            <maxFileSize>2MB</maxFileSize>
            <!-- 最大保存时间：180天-->
            <maxHistory>180</maxHistory>
            <!-- 总文件大小-->
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <!-- 日志格式 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!-- 日志级别过滤器 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 匹配时的操作：接收（记录） -->
            <level>WARN</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!-- 异步输出，提升性能 -->
    <appender name="ASYNC_WARN_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>${ASYNC_QUEUE_SIZE}</queueSize>
        <!-- 队列满时丢弃日志，不阻塞主线程 -->
        <neverBlock>true</neverBlock>
        <!-- 默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>${ASYNC_DISCARDING_THRESHOLD}</discardingThreshold>
        <appender-ref ref="WARN_FILE"/>
    </appender>

    <!-- ERROR日志 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/error.log</file>
        <append>true</append>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/error-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 文件大小限制 -->
            <maxFileSize>2MB</maxFileSize>
            <!-- 最大保存时间：180天-->
            <maxHistory>180</maxHistory>
            <!-- 总文件大小-->
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <!-- 日志格式 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>utf-8</charset>
        </encoder>
        <!-- 日志级别过滤器 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 匹配时的操作：接收（记录） -->
            <level>ERROR</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!-- 异步输出，提升性能 -->
    <appender name="ASYNC_ERROR_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>${ASYNC_QUEUE_SIZE}</queueSize>
        <!-- 队列满时丢弃日志，不阻塞主线程 -->
        <neverBlock>true</neverBlock>
        <!-- 默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold>${ASYNC_DISCARDING_THRESHOLD}</discardingThreshold>
        <appender-ref ref="ERROR_FILE"/>
    </appender>


    <!-- 整合 skywalking 控制台输出 tid -->
    <!--    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">-->
    <!--        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">-->
    <!--            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">-->
    <!--                <pattern>[%tid] ${console.log.pattern}</pattern>-->
    <!--            </layout>-->
    <!--            <charset>utf-8</charset>-->
    <!--        </encoder>-->
    <!--    </appender>-->

    <!-- 整合 skywalking 推送采集日志 -->
    <!--    <appender name="sky_log" class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.log.GRPCLogClientAppender">-->
    <!--        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">-->
    <!--            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">-->
    <!--                <pattern>[%tid] ${console.log.pattern}</pattern>-->
    <!--            </layout>-->
    <!--            <charset>utf-8</charset>-->
    <!--        </encoder>-->
    <!--    </appender>-->

    <!-- ========== 邮件告警 Appender（生产环境可选） ========== -->
    <!--
    <appender name="EMAIL" class="ch.qos.logback.classic.net.SMTPAppender">
        <smtpHost>smtp.company.com</smtpHost>
        <smtpPort>587</smtpPort>
        <STARTTLS>true</STARTTLS>
        <username><EMAIL></username>
        <password>password</password>
        <to><EMAIL></to>
        <to><EMAIL></to>
        <from><EMAIL></from>
        <subject>【生产告警】%logger{20} - %m</subject>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </layout>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <cyclicBufferTracker class="ch.qos.logback.core.spi.CyclicBufferTracker">
            <bufferSize>8</bufferSize>
        </cyclicBufferTracker>
    </appender>
    -->

    <!-- 默认输出 INFO 日志，附加不同环境输出目标 -->
    <springProfile name="dev">
        <!--  MyBatis log configure -->
        <logger name="com.apache.ibatis" level="DEBUG"/>
        <logger name="org.mybatis.spring" level="DEBUG"/>
        <logger name="java.sql.Connection" level="DEBUG"/>
        <logger name="java.sql.Statement" level="DEBUG"/>
        <logger name="java.sql.PreparedStatement" level="DEBUG"/>

        <logger name="org.mybatis.spring" level="INFO"/>
        <logger name="org.springframework" level="INFO"/>
        <logger name="org.springframework.context" level="WARN"/>
        <logger name="org.springframework.beans" level="WARN"/>
        <logger name="com.baomidou.mybatisplus" level="INFO"/>
        <logger name="org.apache.ibatis.io" level="INFO"/>

        <root level="DEBUG">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>

    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <!-- <appender-ref ref="ASYNC_TRACE_FILE"/> -->
            <!-- <appender-ref ref="ASYNC_DEBUG_FILE"/> -->
            <appender-ref ref="ASYNC_INFO_FILE"/>
            <appender-ref ref="ASYNC_WARN_FILE"/>
            <appender-ref ref="ASYNC_ERROR_FILE"/>
        </root>
    </springProfile>
</configuration>
