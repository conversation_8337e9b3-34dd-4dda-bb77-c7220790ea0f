spring:
  application:
    name: qscz-parent
  profiles:
    active: @profiles.active@
  main:
    # 允许循环依赖
    allow-circular-references: true
  config:
    import:
      - classpath:config/web-config.yml
      - classpath:config/database-config.yml
      - classpath:config/redis-config.yml
      - classpath:config/security-config.yml
      - classpath:config/business-config.yml
      - classpath:config/monitoring-config.yml
      - classpath:config/ai-config.yml

  # JDK21开启虚拟线程
  threads:
    virtual:
      enabled: true
  # JackSon配置
  jackson:
    serialization:
      # 时间戳
      write-dates-as-timestamps: true
    deserialization:
      accept-single-value-as-array: true
    time-zone: GMT+8
