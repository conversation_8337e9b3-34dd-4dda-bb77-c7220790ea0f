server:
  port: 8354

# 日志配置
debug: false
logging:
  file:
    path: ./logs
  level:
    root: INFO
    com.songbai: info
    com.baomidou.mybatisplus: INFO
    org.springframework:
      web: INFO
      boot.autoconfigure: ERROR
  config: classpath:logback-spring.xml

spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    dynamic:
      # 设置默认的数据源或者数据源组,默认值即为master
      primary: qscz
      # 严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      strict: false
      datasource:
        qscz:
          type: ${spring.datasource.type}
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **********************************************************************************************************************************
          username: root
          password: ECNHrEmE6dEBAOMAHe!@#

  data:
    redis:
      database: 8
      host: localhost
      password: RedisHrEmE6dEBAOMAHe
      port: 6379
      # 客户端超时时间单位是毫秒
      timeout: 10000

mybatis-plus:
  configuration:
    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl
    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl
    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
