# 数据库相关配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      # 最大连接池数量
      maximum-pool-size: 50
      # 最小空闲线程数量
      minimum-idle: 10
      # 配置获取连接等待超时的时间
      connection-timeout: 30000
      # 校验超时时间
      validation-timeout: 5000
      # 空闲连接存活最大时间，默认10分钟
      idle-timeout: 540000
      # 此属性控制池中连接的最长生命周期，值0表示无限生命周期，默认30分钟
      max-lifetime: 1800000
      # 多久检查一次连接的活性
      keepalive-time: 60000
      # 测试连接是否可用的 SQL，推荐使用快速简单语句
      connection-test-query: SELECT 1
      # 自动提交设置，建议设置为 true，按需设置
      auto-commit: true
      # 连接泄露检测,30秒未归还即记录日志
      leak-detection-threshold: 30000

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.songbai.qscz.project.**.mapper
  global-config:
    banner: false
    dbConfig:
      # 主键类型
      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID
      idType: AUTO
      # 逻辑已删除值(可按需求随意修改)
      logicDeleteValue: 1
      # 逻辑未删除值
      logicNotDeleteValue: 0
      insertStrategy: NOT_NULL
      updateStrategy: NOT_NULL
      whereStrategy: NOT_NULL
  configuration:
    # 自动驼峰命名规则（camel case）映射
    mapUnderscoreToCamelCase: true
    # MyBatis 自动映射策略
    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射
    autoMappingBehavior: FULL
    # MyBatis 自动映射时未知列或未知属性处理策
    # NONE：不做处理 WARNING：打印相关警告 FAILING：抛出异常和详细信息
    autoMappingUnknownColumnBehavior: NONE
    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl
    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl
    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
  mapper-package: com.songbai.qscz.project.**.mapper

# 数据加密
mybatis-encryptor:
  # 是否开启加密
  enable: false
  # 默认加密算法
  algorithm: BASE64
  # 编码方式 BASE64/HEX。默认BASE64
  encode: BASE64
  # 安全秘钥 对称算法的秘钥 如：AES，SM4
  password: ""
  # 公私钥 非对称算法的公私钥 如：SM2，RSA
  public-key: ""
  private-key: ""
