# 安全相关配置

# Sa-Token配置
sa-token:
  is-print: false
  # token 名称（同时也是 cookie 名称）
  token-name: Authorization
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # token前缀
  token-prefix: "Bearer"
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  # 允许动态设置 token 有效期
  dynamic-active-timeout: true
  # 允许从 请求参数 读取 token
  is-read-body: true
  # 允许从 header 读取 token
  is-read-header: true
  # 关闭 cookie 鉴权 从根源杜绝 csrf 漏洞风险
  is-read-cookie: false
  # jwt秘钥
  jwt-secret-key: 53ace09d87b778dhi6203ca2001c695d688130abe8fa6e39a495d3abce12f58b
  # 安全配置
  security:
    excludes:
      - /doc/**
      - /*/open/**
      - /open/**

# API接口加密
api-decrypt:
  # 是否开启全局接口加密
  enabled: false
  # AES 加密头标识
  header-flag: encrypt-key
  # 响应加密公钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换
  public-key: ""
  # 请求解密私钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换
  private-key: ""

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认5分钟）
    lockTime: 5

#验证码
captcha:
  # 是否启用验证码校验
  enable: false
  # 验证码类型 math 数组计算 char 字符验证
  type: MATH
  # line 线段干扰 circle 圆圈干扰 shear 扭曲干扰
  category: CIRCLE
  # 数字验证码位数
  numberLength: 1
  # 字符验证码长度
  charLength: 4
