# Web和服务器相关配置
spring:
  web:
    resources:
      add-mappings: true
      static-locations: classpath:/resources/,classpath:/static/
  mvc:
    static-path-pattern: /**

  servlet:
    multipart:
      max-file-size: 300MB
      max-request-size: 300MB

  task:
    execution:
      pool:
        # 使用虚拟线程时可以设置更大的值
        # 线程池创建时的初始化线程数，默认为8
        core-size: 20
        # 线程池的最大线程数，默认为int最大值
        max-size: 20
        # 用来缓冲执行任务的队列，默认为int最大值
        queue-capacity: 30
        # 线程终止前允许保持空闲的时间
        keep-alive: 60s
        # 是否允许核心线程超时
        allow-core-thread-timeout: true
      shutdown:
        # 是否等待剩余任务完成后才关闭应用
        await-termination: false
        # 等待剩余任务完成的最大时间
        await-termination-period: 30
      # 线程名的前缀
      thread-name-prefix: task-

server:
  servlet:
    context-path: /
  undertow:
    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的
    max-http-post-size: -1
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分
    buffer-size: 512
    # 是否分配的直接内存
    direct-buffers: true
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 8
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 256

# 全局线程池相关配置
# 如使用JDK21请直接使用虚拟线程 不要开启此配置
thread-pool:
  # 是否开启线程池
  enabled: false
  # 队列最大长度
  queue-capacity: 128
  # 线程池维护线程所允许的空闲时间
  keep-alive-seconds: 300

# XSS配置
xss:
  enabled: false
  exclude-urls:
    - /system/notice

# Web请求性能日志
web-log:
  # 是否启用性能统计
  enable-performance: true
  # 慢请求阈值（毫秒），只对慢请求记录警告日志
  slow-request-threshold: 1500
  # 是否记录详细参数
  enable-detail-params: true
