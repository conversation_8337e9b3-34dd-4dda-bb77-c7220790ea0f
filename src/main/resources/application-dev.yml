server:
  port: 6006

# 日志配置
debug: true
logging:
  file:
    path: ./logs
  level:
    root: info
    com.songbai: debug
    com:
      alibaba:
        cloud: debug
    com.baomidou.mybatisplus: INFO
    org.springframework:
      ai: debug
      web: info
      boot.autoconfigure: ERROR
  config: classpath:logback-spring.xml

spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    # 动态数据源文档：https://www.xiaojingge.com/archives/ef008289-8ab8-485c-8019-2703267c7beb
    # TODO  MybatisPlus内置的ServiceImpl在新增，更改，删除等一些方法上自带事物导致不能切换数据源。
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: true
      # 设置默认的数据源或者数据源组,默认值即为master
      primary: qscz
      # 严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      strict: false
      datasource:
        qscz:
          type: ${spring.datasource.type}
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **************************************************************************************************************************************
          username: root
          password: baomahe123
  data:
    redis:
      host: *************
      port: 6379
      timeout: 10s

mybatis-plus:
  configuration:
    # 更详细的日志输出 会有性能损耗 org.apache.ibatis.logging.stdout.StdOutImpl
    # 关闭日志记录 (可单纯使用 p6spy 分析) org.apache.ibatis.logging.nologging.NoLoggingImpl
    # 默认日志输出 org.apache.ibatis.logging.slf4j.Slf4jImpl
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
