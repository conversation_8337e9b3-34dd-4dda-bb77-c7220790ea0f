<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.songbai.qscz.project.tools.aba.mapper.AbaPlanProjectGoalMapper">
    <resultMap id="BaseResultMap" type="com.songbai.qscz.project.tools.aba.model.AbaPlanProjectGoal">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="org_id" jdbcType="INTEGER" property="orgId"/>
        <result column="child_id" jdbcType="INTEGER" property="childId"/>
        <result column="plan_id" jdbcType="INTEGER" property="planId"/>
        <result column="domain_id" jdbcType="INTEGER" property="domainId"/>
        <result column="plan_project_id" jdbcType="INTEGER" property="planProjectId"/>
        <result column="project_id" jdbcType="INTEGER" property="projectId"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="short_goal_id" jdbcType="INTEGER" property="shortGoalId"/>
        <result column="short_goal_name" jdbcType="VARCHAR" property="shortGoalName"/>
        <result column="assist_method" jdbcType="VARCHAR" property="assistMethod"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="record_type" jdbcType="INTEGER" property="recordType"/>
        <result column="opt_type" jdbcType="INTEGER" property="optType"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="target" jdbcType="LONGVARCHAR" property="target"/>
    </resultMap>


</mapper>
