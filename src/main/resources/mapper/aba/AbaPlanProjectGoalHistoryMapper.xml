<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.songbai.qscz.project.tools.aba.mapper.AbaPlanProjectGoalHistoryMapper">

    <resultMap id="AbaPlanProjectGoalHistoryMap" type="com.songbai.qscz.project.tools.aba.model.AbaPlanProjectGoalHistory">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="orgId" column="org_id" jdbcType="INTEGER"/>
        <result property="childId" column="child_id" jdbcType="INTEGER"/>
        <result property="planId" column="plan_id" jdbcType="INTEGER"/>
        <result property="domainId" column="domain_id" jdbcType="INTEGER"/>
        <result property="planProjectId" column="plan_project_id" jdbcType="INTEGER"/>
        <result property="projectId" column="project_id" jdbcType="INTEGER"/>
        <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
        <result property="planProjectGoalId" column="plan_project_goal_id" jdbcType="INTEGER"/>
        <result property="shortGoalId" column="short_goal_id" jdbcType="INTEGER"/>
        <result property="shortGoalName" column="short_goal_name" jdbcType="VARCHAR"/>
        <result property="assistMethod" column="assist_method" jdbcType="VARCHAR"/>
        <result property="target" column="target" jdbcType="VARCHAR"/>
        <result property="recordType" column="record_type" jdbcType="INTEGER"/>
        <result property="optType" column="opt_type" jdbcType="INTEGER"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
        <result property="batchNo" column="batch_no" jdbcType="INTEGER"/>
        <result property="userType" column="user_type" jdbcType="INTEGER"/>
    </resultMap>
    <select id="selectListByChildId" resultType="com.songbai.qscz.project.tools.aba.model.AbaPlanProjectGoalHistory">
        select
            appgh.*,
            ap.level AS projectLevel
        from aba_plan_project_goal_history appgh
        left join aba_project ap on appgh.project_id = ap.id
        where appgh.child_id = #{childId}
        order by appgh.create_time desc
    </select>
    <select id="selectPassByPlanGoalId" resultType="com.songbai.qscz.project.tools.aba.model.AbaPlanProjectGoalHistory">
        SELECT
        appgh.*,
        ap.level AS projectLevel
        FROM aba_plan_project_goal_history appgh
        LEFT JOIN aba_project ap ON appgh.project_id = ap.id
        WHERE appgh.plan_project_goal_id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY appgh.create_time DESC
    </select>

</mapper>

