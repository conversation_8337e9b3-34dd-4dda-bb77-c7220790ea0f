{"serverUrl": "http://*************:8021/", "outPath": "./src/main/resources/static/doc", "isStrict": false, "allInOne": true, "coverOld": true, "style": "xt256", "createDebugPage": true, "skipTransientField": true, "requestFieldToUnderline": false, "responseFieldToUnderline": false, "allInOneDocFileName": "index.html", "revisionLogs": [{"version": "1.0", "status": "新增", "author": "李兵兵", "remarks": "第一版"}], "requestHeaders": [{"name": "Authorization", "type": "string", "desc": "登录token", "value": "Bearer xxxxxxxxxxxxxxx", "required": true, "since": "-", "pathPatterns": "/**", "excludePathPatterns": "/doc/**,/download/**,/**/open/**,/**/**/open/**,/file/**,/app/appVersion/**,/app/sys/appLogin"}]}