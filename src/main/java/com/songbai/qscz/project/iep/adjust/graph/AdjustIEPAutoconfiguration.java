package com.songbai.qscz.project.iep.adjust.graph;

import cn.hutool.json.JSONUtil;
import com.alibaba.cloud.ai.graph.*;
import com.alibaba.cloud.ai.graph.agent.ReactAgent;
import com.alibaba.cloud.ai.graph.exception.GraphStateException;
import com.alibaba.cloud.ai.graph.state.strategy.ReplaceStrategy;
import com.songbai.qscz.project.iep.adjust.prompt.AdjustIEPPrompt;
import com.songbai.qscz.project.tools.aba.tool.AbaPlanTool;
import com.songbai.qscz.project.tools.core.ToolsBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.util.Timeout;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.tool.definition.ToolDefinition;
import org.springframework.ai.tool.method.MethodToolCallback;
import org.springframework.ai.tool.resolution.ToolCallbackResolver;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.client.RestClient;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.alibaba.cloud.ai.graph.StateGraph.END;
import static com.alibaba.cloud.ai.graph.StateGraph.START;
import static com.alibaba.cloud.ai.graph.action.AsyncEdgeAction.edge_async;

/**
 * 调整IEP智能体配置
 *
 * <AUTHOR>
 * @since 2025-08-11 9:28
 */
@Slf4j
@Configuration
public class AdjustIEPAutoconfiguration {

    // TODO 调整结果判断是否合理智能体
    // TODO 调整结果执行智能体
    // TODO 详细查看各种配置
    // TODO 替换SimpleLoggerAdvisor

    // TODO 记忆功能
    // TODO 会话隔离
    // TODO toolInput格式问题

    @Bean
    public ReactAgent adjustDecisionAgent(ChatModel chatModel, AbaPlanTool abaPlanTool) throws GraphStateException {
        // 构建工具
        Method getAbaPlanDetailMethod = ReflectionUtils.findMethod (AbaPlanTool.class, "getAbaPlanDetailMethod", String.class, ToolContext.class);
        MethodToolCallback toolCallback = MethodToolCallback.builder ()
            .toolObject (abaPlanTool)
            .toolMethod (getAbaPlanDetailMethod)
            .toolDefinition (ToolDefinition.builder () .name (AbaPlanTool.TOOL_NAME).inputSchema (AbaPlanTool.TOOL_PARAMETERS)
                .description (AbaPlanTool.TOOL_DESCRIPTION).build ())
            .build ();

        // 构建客户端
        ChatClient chatClient = ChatClient.builder(chatModel)
            .defaultSystem (AdjustIEPPrompt.ADJUST_DECISION_SYSTEM_PROMPT)
            .defaultAdvisors(new SimpleLoggerAdvisor())
            .defaultOptions(OpenAiChatOptions.builder().internalToolExecutionEnabled(false).build())
            // .defaultAdvisors(new MessageChatMemoryAdvisor(new InMemoryChatMemory()))
            .defaultTools (abaPlanTool)
            .build();

        // 构建Agent
        ReactAgent adjustDecisionAgent = ReactAgent.builder ()
            .name ("adjustDecisionAgent")
            .chatClient (chatClient)
            .tools (List.of (toolCallback))
            .maxIterations (10)
            .build ();
        adjustDecisionAgent.getAndCompileGraph ();
        return adjustDecisionAgent;
    }

    @Bean
    public ReactAgent projectAdjustAgent(ChatModel chatModel, ToolCallbackResolver resolver) throws GraphStateException {
        ChatClient chatClient = ChatClient.builder(chatModel)
            .defaultSystem (AdjustIEPPrompt.PROJECT_ADJUST_SYSTEM_PROMPT)
            // .defaultToolNames(AbaPlanTool.TOOL_NAME)
            .defaultAdvisors(new SimpleLoggerAdvisor())
            .defaultOptions(OpenAiChatOptions.builder().internalToolExecutionEnabled(false).build())
            // .defaultAdvisors(new MessageChatMemoryAdvisor(new InMemoryChatMemory()))
            // .defaultToolCallbacks (ToolsBuilder.getAdjustIEPAgentToolCalls ())
            .build();

        ReactAgent projectAdjustAgent = ReactAgent.builder ()
            .name ("projectAdjustAgent")
            .chatClient (chatClient)
            .tools (ToolsBuilder.getAdjustIEPAgentToolCalls ())
            // .resolver (resolver)
            .maxIterations (10)
            .build ();
        projectAdjustAgent.getAndCompileGraph ();
        return projectAdjustAgent;
    }
    @Bean
    public ReactAgent goalAdjustAgent(ChatModel chatModel, ToolCallbackResolver resolver) throws GraphStateException {
        ChatClient chatClient = ChatClient.builder(chatModel)
            .defaultSystem (AdjustIEPPrompt.GOAL_ADJUST_SYSTEM_PROMPT)
            // .defaultToolNames(AbaPlanTool.TOOL_NAME)
            .defaultAdvisors(new SimpleLoggerAdvisor())
            .defaultOptions(OpenAiChatOptions.builder().internalToolExecutionEnabled(false).build())
            // .defaultAdvisors(new MessageChatMemoryAdvisor(new InMemoryChatMemory()))
            // .defaultToolCallbacks (ToolsBuilder.getAdjustIEPAgentToolCalls ())
            .build();

        ReactAgent goalAdjustAgent = ReactAgent.builder ()
            .name ("goalAdjustAgent")
            .chatClient (chatClient)
            .tools (ToolsBuilder.getAdjustIEPAgentToolCalls ())
            // .resolver (resolver)
            .maxIterations (10)
            .build ();
        goalAdjustAgent.getAndCompileGraph ();
        return goalAdjustAgent;
    }
    @Bean
    public ReactAgent targetAdjustAgent(ChatModel chatModel, ToolCallbackResolver resolver) throws GraphStateException {
        ChatClient chatClient = ChatClient.builder(chatModel)
            .defaultSystem (AdjustIEPPrompt.TARGET_ADJUST_SYSTEM_PROMPT)
            // .defaultToolNames(AbaPlanTool.TOOL_NAME)
            .defaultAdvisors(new SimpleLoggerAdvisor())
            .defaultOptions(OpenAiChatOptions.builder().internalToolExecutionEnabled(false).build())
            // .defaultAdvisors(new MessageChatMemoryAdvisor(new InMemoryChatMemory()))
            // .defaultToolCallbacks (ToolsBuilder.getAdjustIEPAgentToolCalls ())
            .build();

        ReactAgent targetAdjustAgent = ReactAgent.builder ()
            .name ("targetAdjustAgent")
            .chatClient (chatClient)
            .tools (ToolsBuilder.getAdjustIEPAgentToolCalls ())
            // .resolver (resolver)
            .maxIterations (10)
            .build ();
        targetAdjustAgent.getAndCompileGraph ();
        return targetAdjustAgent;

    }
    @Bean
    public ReactAgent supportAdjustAgent(ChatModel chatModel, ToolCallbackResolver resolver) throws GraphStateException {
        ChatClient chatClient = ChatClient.builder(chatModel)
            .defaultSystem (AdjustIEPPrompt.SUPPORT_ADJUST_SYSTEM_PROMPT)
            .defaultToolNames(AbaPlanTool.TOOL_NAME)
            .defaultAdvisors(new SimpleLoggerAdvisor())
            .defaultOptions(OpenAiChatOptions.builder().internalToolExecutionEnabled(false).build())
            // .defaultAdvisors(new MessageChatMemoryAdvisor(new InMemoryChatMemory()))
            // .defaultToolCallbacks (ToolsBuilder.getAdjustIEPAgentToolCalls ())
            .build();

        ReactAgent supportAdjustAgent = ReactAgent.builder ()
            .name ("supportAdjustAgent")
            .chatClient (chatClient)
            .tools (ToolsBuilder.getAdjustIEPAgentToolCalls ())
            // .resolver (resolver)
            .maxIterations (10)
            .build ();
        supportAdjustAgent.getAndCompileGraph ();
        return supportAdjustAgent;
    }

    @Bean
    public StateGraph adjustIEPGraph(@Qualifier ("adjustDecisionAgent") ReactAgent adjustDecisionAgent,
                                         @Qualifier ("projectAdjustAgent") ReactAgent projectAdjustAgent,
                                         @Qualifier ("goalAdjustAgent") ReactAgent goalAdjustAgent,
                                         @Qualifier ("targetAdjustAgent") ReactAgent targetAdjustAgent,
                                         @Qualifier ("supportAdjustAgent") ReactAgent supportAdjustAgent)
        throws GraphStateException {

        // 设置全局状态 OverAllState：定义一个 OverAllStateFactory，用于在每次执行工作流时创建初始的全局状态对象。通过注册若干 Key 及其更新策略来管理上下文数据
        KeyStrategyFactory keyStrategyFactory = () -> {
            HashMap<String, KeyStrategy> keyStrategyHashMap = new HashMap<>();
            keyStrategyHashMap.put ("input", new ReplaceStrategy ());
            keyStrategyHashMap.put ("adjustDecision", new ReplaceStrategy ());
            keyStrategyHashMap.put ("adjustResult", new ReplaceStrategy ());
            return keyStrategyHashMap;
        };


        StateGraph stateGraph = new StateGraph ("调整IEP流程编排", keyStrategyFactory)
            // 定义节点
            .addNode ("adjustDecisionAgent", adjustDecisionAgent.asAsyncNodeAction ("input", "adjustDecision"))
            .addNode ("projectAdjustAgent", projectAdjustAgent.asAsyncNodeAction ("adjustDecision", "adjustResult"))
            .addNode ("goalAdjustAgent", goalAdjustAgent.asAsyncNodeAction ("adjustDecision", "adjustResult"))
            .addNode ("targetAdjustAgent", targetAdjustAgent.asAsyncNodeAction ("adjustDecision", "adjustResult"))
            .addNode ("supportAdjustAgent", supportAdjustAgent.asAsyncNodeAction ("adjustDecision", "adjustResult"))

            // 定义边（流程顺序）
            .addEdge (START, "adjustDecisionAgent")
            .addConditionalEdges ("adjustDecisionAgent",
                edge_async (state -> {
                    String adjustDecision = (String) state.value ("adjustDecision").orElse ("");
                    // 去除 markdown code block
                    adjustDecision = adjustDecision.trim ();
                    if (adjustDecision.startsWith ("```json")) {
                        adjustDecision = adjustDecision.replaceFirst ("^```json", "").trim ();
                    }
                    if (adjustDecision.startsWith ("```")) {
                        adjustDecision = adjustDecision.replaceFirst ("^```", "").trim ();
                    }
                    if (adjustDecision.endsWith ("```")) {
                        adjustDecision = adjustDecision.replaceAll ("```$", "").trim ();
                    }
                    // 解析 JSON 并提取 recommend 字段
                    try {
                        String recommend = JSONUtil.parseObj (adjustDecision).getStr ("recommend");
                        log.debug("提取到的推荐策略: {}", recommend);
                        return recommend;
                    } catch (Exception e) {
                        log.warn("解析决策结果失败，使用默认策略: {}", adjustDecision, e);
                        return "SUPPORT"; // 默认策略
                    }
                }),
                Map.of ("PROJECT", "projectAdjustAgent",
                    "GOAL", "goalAdjustAgent",
                    "TARGET", "targetAdjustAgent",
                    "SUPPORT", "supportAdjustAgent"))
            // 图的结束节点
            .addEdge ("projectAdjustAgent", END)
            .addEdge ("goalAdjustAgent", END)
            .addEdge ("targetAdjustAgent", END)
            .addEdge ("supportAdjustAgent", END);

        GraphRepresentation graphRepresentation = stateGraph.getGraph (GraphRepresentation.Type.PLANTUML, "调整IEP流程编排");

        log.debug ("\n\n");
        log.debug (graphRepresentation.content ());
        log.debug ("\n\n");

        return stateGraph;
    }

    @Bean
    public RestClient.Builder createRestClient() {

        // 2. 创建 RequestConfig 并设置超时
        RequestConfig requestConfig = RequestConfig.custom()
            .setConnectTimeout(Timeout.of(10, TimeUnit.MINUTES))
            .setResponseTimeout(Timeout.of(10, TimeUnit.MINUTES))
            .setConnectionRequestTimeout(Timeout.of(10, TimeUnit.MINUTES))
            .build();

        // 3. 创建 CloseableHttpClient 并应用配置
        HttpClient httpClient = HttpClients.custom().setDefaultRequestConfig(requestConfig).build();

        // 4. 使用 HttpComponentsClientHttpRequestFactory 包装 HttpClient
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);

        // 5. 创建 RestClient 并设置请求工厂
        return RestClient.builder().requestFactory(requestFactory);
    }

}
