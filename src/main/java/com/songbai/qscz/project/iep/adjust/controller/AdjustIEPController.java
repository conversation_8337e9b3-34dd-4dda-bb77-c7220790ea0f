package com.songbai.qscz.project.iep.adjust.controller;

import cn.hutool.core.lang.UUID;
import com.alibaba.cloud.ai.graph.*;
import com.alibaba.cloud.ai.graph.checkpoint.config.SaverConfig;
import com.alibaba.cloud.ai.graph.checkpoint.constant.SaverConstant;
import com.alibaba.cloud.ai.graph.checkpoint.savers.MemorySaver;
import com.alibaba.cloud.ai.graph.exception.GraphRunnerException;
import com.alibaba.cloud.ai.graph.exception.GraphStateException;
import com.alibaba.cloud.ai.graph.observation.GraphObservationLifecycleListener;
import com.songbai.qscz.common.ai.graph.GraphId;
import com.songbai.qscz.common.ai.graph.GraphProcess;
import io.micrometer.observation.ObservationRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Optional;

/**
 * 调整IEP接口
 *
 * <AUTHOR>
 * @since 2025-08-21 9:39
 */
@Slf4j
@RestController
@RequestMapping("/open/iep/adjust")
public class AdjustIEPController {

    private final CompiledGraph compiledGraph;

    private final GraphProcess graphProcess;

    @Autowired
    public AdjustIEPController(@Qualifier ("adjustIEPGraph") StateGraph adjustIEPGraph, ObjectProvider<ObservationRegistry> observationRegistry)
        throws GraphStateException {
        SaverConfig saverConfig = SaverConfig.builder().register(SaverConstant.MEMORY, new MemorySaver ()).build();
        this.compiledGraph = adjustIEPGraph.compile(CompileConfig.builder()
            .saverConfig(saverConfig)
            // .interruptBefore("human_feedback")
            .withLifecycleListener(new GraphObservationLifecycleListener (
                observationRegistry.getIfUnique(() -> ObservationRegistry.NOOP)))
            .build());
        // 设置最大迭代次数
        // TODO 设置到配置文件中
        this.compiledGraph.setMaxIterations(10);
        this.graphProcess = new GraphProcess (this.compiledGraph);
        log.info("ChatController initialized with graph maxIterations: {}",10);
    }


    /**
     * ChatClient 简单调用
     */
    @GetMapping ("/chat")
    public String simpleChat() throws GraphRunnerException {
        String query = """
        {
            "childId": "1",
            "planId": "31"
            "planProjectId": "3"
        }
        """;

        // 创建线程ID
        String uuid = UUID.fastUUID ().toString ();
        GraphId graphId = graphProcess.createNewGraphId(uuid);

        RunnableConfig runnableConfig = RunnableConfig.builder().threadId(UUID.fastUUID ().toString ()).build();
        Optional<OverAllState> result = compiledGraph.invoke (Map.of ("input", query), runnableConfig);

        // 获取最终的调整结果
        String adjustResult = (String) result.get().value("adjustResult").orElse("未获取到调整结果");

        return adjustResult;
    }
}
