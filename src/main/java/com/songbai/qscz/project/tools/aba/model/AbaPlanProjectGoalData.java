package com.songbai.qscz.project.tools.aba.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("aba_plan_project_goal_data")
public class AbaPlanProjectGoalData implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 机构ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 儿童ID
     */
    @TableField("child_id")
    private Integer childId;

    /**
     * 计划ID
     */
    @TableField("plan_id")
    private Integer planId;

    /**
     * 领域ID
     */
    @TableField("domain_id")
    private Integer domainId;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 计划项目ID
     */
    @TableField("plan_project_id")
    private Integer planProjectId;

    /**
     * 计划项目目标ID
     */
    @TableField("plan_project_goal_id")
    private Integer planProjectGoalId;

    /**
     * 短期目标ID
     */
    @TableField("short_goal_id")
    private Integer shortGoalId;

    /**
     * 短期目标名称
     */
    @TableField("short_goal_name")
    private String shortGoalName;

    /**
     * 辅助方式
     */
    @TableField("assist_method")
    private String assistMethod;

    /**
     * 记录方式（1回合制 2试探 3分步骤 ）
     */
    @TableField("record_type")
    private Integer recordType;

    /**
     * 回合制是否达标(3天连续达到80%（含），或2天连续达到90%（含），或1天100%)
     */
    @TableField("is_continuous_pass")
    private Integer isContinuousPass;

    /**
     * 回合制是否异常(3天连续在50%（含）以下，或2天连续在30%（含）以下)
     */
    @TableField("is_fail")
    private Integer isFail;

    /**
     * 回合制是否缓慢(连续5天，在51%（含）-79%（含）之间忽上忽下)
     */
    @TableField("is_slow")
    private Integer isSlow;

    /**
     * 分步骤是否通过(3天连续100%)
     */
    @TableField("is_step_pass")
    private Integer isStepPass;

    /**
     * 试探总小目标数量
     */
    @TableField("try_item_total")
    private Integer tryItemTotal;

    /**
     * 试探通过的小目标数量
     */
    @TableField("try_pass_count")
    private Integer tryPassCount;

    /**
     * 计算来源 具体哪天通过的，逗号分隔
     */
    @TableField("calc_source")
    private String calcSource;

    /**
     * 状态((1进行中 2通过 3挂起)
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 小目标结果json
     */
    @TableField("target_result")
    private String targetResult;

    @Serial
    private static final long serialVersionUID = 1L;
}
