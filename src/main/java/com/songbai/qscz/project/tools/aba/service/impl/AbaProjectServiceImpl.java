package com.songbai.qscz.project.tools.aba.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.songbai.qscz.common.dynamicDatasource.QSCZ;
import com.songbai.qscz.project.tools.aba.mapper.AbaProjectMapper;
import com.songbai.qscz.project.tools.aba.model.AbaProject;
import com.songbai.qscz.project.tools.aba.service.AbaProjectService;
import org.springframework.stereotype.Service;

/**
 * (AbaProject)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-27 10:22:32
 */

@Service ("abaProjectService")
public class AbaProjectServiceImpl extends ServiceImpl<AbaProjectMapper,AbaProject> implements AbaProjectService {

}
