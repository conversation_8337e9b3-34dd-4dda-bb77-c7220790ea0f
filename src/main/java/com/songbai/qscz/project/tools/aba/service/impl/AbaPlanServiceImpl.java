package com.songbai.qscz.project.tools.aba.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.songbai.qscz.project.tools.aba.mapper.*;
import com.songbai.qscz.project.tools.aba.model.*;
import com.songbai.qscz.project.tools.aba.service.AbaPlanService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
public class AbaPlanServiceImpl extends ServiceImpl<AbaPlanMapper, AbaPlan> implements AbaPlanService {

    @Resource
    private AbaPlanProjectGoalMapper abaPlanProjectGoalMapper;
    @Resource
    private AbaDomainMapper abaDomainMapper;
    @Resource
    private AbaPlanProjectGoalHistoryMapper abaPlanProjectGoalHistoryMapper;
    @Resource
    private AbaDailyGoalDataMapper abaDailyGoalDataMapper;
    @Resource
    private AbaPlanProjectGoalDataMapper abaPlanProjectGoalDataMapper;


}
