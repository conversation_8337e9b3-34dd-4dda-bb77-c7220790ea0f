package com.songbai.qscz.project.tools.core;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 工具执行结果
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ToolExecuteResult {

    public ToolExecuteResult (String output) {
        setOutput (output);
    }

    public ToolExecuteResult (String output, boolean interrupted) {
        setOutput (output);
        setInterrupted (interrupted);
    }

    /**
     * 工具返回的内容
     */
    private String output;

    /**
     * 是否中断
     */
    private boolean interrupted;

}
