/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.songbai.qscz.project.tools.core;

import org.springframework.ai.tool.ToolCallback;

import java.util.ArrayList;
import java.util.List;

/**
 * 工具链构建
 *
 * <AUTHOR>
 */
public class ToolsBuilder {

    /**
     * 获取调整IEP智能体工具链
     *
     * @return
     */
    public static List<ToolCallback> getAdjustIEPAgentToolCalls () {
        // Method getAbaPlanDetailMethod = ReflectionUtils.findMethod (AbaPlanTool.class, "getAbaPlanDetailMethod", String.class, ToolContext.class);
        // MethodToolCallback toolCallback = MethodToolCallback.builder ()
        //     .toolObject (abaPlanTool)
        //     .toolMethod (getAbaPlanDetailMethod)
        //     .toolDefinition (ToolDefinition.builder () .name (AbaPlanTool.TOOL_NAME).inputSchema (AbaPlanTool.TOOL_PARAMETERS)
        //         .description (AbaPlanTool.TOOL_DESCRIPTION).build ())
        //     .build ();
        return new ArrayList<> ();
    }

}
