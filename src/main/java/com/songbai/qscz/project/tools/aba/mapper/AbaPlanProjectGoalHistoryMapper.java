package com.songbai.qscz.project.tools.aba.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.songbai.qscz.common.dynamicDatasource.QSCZ;
import com.songbai.qscz.project.tools.aba.model.AbaPlanProjectGoalHistory;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface AbaPlanProjectGoalHistoryMapper extends BaseMapper<AbaPlanProjectGoalHistory> {
    List<AbaPlanProjectGoalHistory> selectListByChildId(@Param("childId") Integer childId);

    /**
     * 根据通过的计划短期目标id查询短期目标操作历史
     * @param planProjectGoalIdList id列表
     * @return 历史列表
     */
    List<AbaPlanProjectGoalHistory> selectPassByPlanGoalId(@Param("list") List<Integer> planProjectGoalIdList);
}
