package com.songbai.qscz.project.tools.aba.tool;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.songbai.qscz.project.tools.aba.model.AbaPlan;
import com.songbai.qscz.project.tools.aba.service.AbaPlanService;
import com.songbai.qscz.project.tools.core.ToolExecuteResult;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Service;

/**
 * ABA计划工具
 *
 * <AUTHOR>
 * @since 2025-08-20 11:14
 */
@Slf4j
@Service
public class AbaPlanTool {

    /**
     * 工具名称
     */
    public static final String TOOL_NAME = "getAbaPlanDetail";

    /**
     * 工具执行所需参数
     */
    public static final String TOOL_PARAMETERS = """
        {
            "type": "object",
            "properties": {
                "planId": {
                    "type": "string",
                    "description": "(required) Primary key ID of the ABA plan."
                }
            },
            "required": ["planId"]
        }
        """;

    /**
     * 工具介绍
     */
    public static final String TOOL_DESCRIPTION = """
        从数据库中查询指定计划ID的完整计划详情。
        当你需要获取指定计划的完整信息时，可以使用此工具。
        该工具会返回包含领域、项目、短期目标、小目标、辅助方式等信息的完整计划详情。

        Query the complete plan details for a specified plan ID from the database.
        When you need to retrieve the full information of a specific plan, you can use this tool.
        The tool will return the complete plan details, including fields such as domain, project, short goals, targets, and auxiliary methods.
        """;


    @Resource
    private AbaPlanService planService;

    /**
     * 执行工具
     *
     * @param toolInput 工具输入
     * @return
     */
    @Tool(name = AbaPlanTool.TOOL_NAME, description = AbaPlanTool.TOOL_DESCRIPTION)
    public ToolExecuteResult getAbaPlanDetailMethod (@ToolParam(description = "getAbaPlanDetail工具的参数") String toolInput, ToolContext context) {
        log.info ("ABAPlanTool toolInput:{}", toolInput);
        JSONObject toolInputMap = JSONUtil.parseObj (toolInput);
        Integer planId = toolInputMap.getInt ("planId");

        //从数据库中查询指定计划ID的完整计划详情
        AbaPlan abaPlan = planService.getById (planId);
        // TODO 补充其他详细信息

        return new ToolExecuteResult (JSONUtil.toJsonStr (abaPlan));
    }

}
