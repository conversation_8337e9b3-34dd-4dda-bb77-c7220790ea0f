package com.songbai.qscz.project.tools.aba.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.songbai.qscz.common.dynamicDatasource.QSCZ;
import com.songbai.qscz.project.tools.aba.model.AbaDailyGoalData;
import org.apache.ibatis.annotations.Param;

import java.util.Date;


public interface AbaDailyGoalDataMapper extends BaseMapper<AbaDailyGoalData> {
    AbaDailyGoalData selectOneByPlanGoalIdAndData(@Param("type") Integer type,@Param("childId") Integer childId,@Param("shortGoalId") Integer shortGoalId,@Param("date") Date date);
}
