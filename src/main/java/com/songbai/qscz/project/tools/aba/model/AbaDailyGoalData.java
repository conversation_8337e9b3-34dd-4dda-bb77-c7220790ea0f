package com.songbai.qscz.project.tools.aba.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("aba_daily_goal_data")
public class AbaDailyGoalData implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 日期
     */
    @TableField("date")
    private Date date;

    /**
     * 机构ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 儿童ID
     */
    @TableField("child_id")
    private Integer childId;

    /**
     * 计划ID
     */
    @TableField("plan_id")
    private Integer planId;

    /**
     * 计划项目ID
     */
    @TableField("plan_project_id")
    private Integer planProjectId;

    /**
     * 计划项目目标ID
     */
    @TableField("plan_project_goal_id")
    private Integer planProjectGoalId;

    /**
     * 短期目标ID
     */
    @TableField("short_goal_id")
    private Integer shortGoalId;

    /**
     * 短期目标名称
     */
    @TableField("short_goal_name")
    private String shortGoalName;

    /**
     * 辅助方式
     */
    @TableField("assist_method")
    private String assistMethod;

    /**
     * 小目标结果
     */
    @TableField("target_result")
    private String targetResult;

    /**
     * 记录方式（1回合制 2试探 3分步骤 ）
     */
    @TableField("record_type")
    private Integer recordType;

    /**
     * 当天课程IDS，以,分割
     */
    @TableField("course_ids")
    private String courseIds;

    /**
     * 当天课程项目IDS，以,分割
     */
    @TableField("course_project_ids")
    private String courseProjectIds;

    /**
     * 当天课程项目短期目标IDS，以,分割
     */
    @TableField("course_project_goal_ids")
    private String courseProjectGoalIds;

    /**
     * 领域ID
     */
    @TableField("domain_id")
    private Integer domainId;

    /**
     * 领域名称
     */
    @TableField("domain_name")
    private String domainName;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 项目名称
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 项目阶段
     */
    @TableField("project_level")
    private Integer projectLevel;

    /**
     * 通过率
     */
    @TableField("pass_rate")
    private Double passRate;

    /**
     * 回合制是否达标(3天连续达到80%（含），或2天连续达到90%（含），或1天100%)
     */
    @TableField("is_continuous_pass")
    private Integer isContinuousPass;

    /**
     * 回合制是否异常(3天连续在50%（含）以下，或2天连续在30%（含）以下)
     */
    @TableField("is_fail")
    private Integer isFail;

    /**
     * 回合制是否缓慢(连续5天，在51%（含）-79%（含）之间忽上忽下)
     */
    @TableField("is_slow")
    private Integer isSlow;

    /**
     * 分步骤是否通过(3天连续100%)
     */
    @TableField("is_step_pass")
    private Integer isStepPass;

    /**
     * '试探小目标累计数量'
     */
    @TableField("try_item_total")
    private Integer tryItemTotal;

    /**
     * 试探通过的小目标累计数量
     */
    @TableField("try_pass_count")
    private Integer tryPassCount;


    @Serial
    private static final long serialVersionUID = 1L;
}
