package com.songbai.qscz.project.tools.aba.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.songbai.qscz.common.dynamicDatasource.QSCZ;
import com.songbai.qscz.project.tools.aba.mapper.AbaPlanProjectGoalMapper;
import com.songbai.qscz.project.tools.aba.mapper.AbaPlanProjectMapper;
import com.songbai.qscz.project.tools.aba.model.AbaPlanProject;
import com.songbai.qscz.project.tools.aba.model.AbaPlanProjectGoal;
import com.songbai.qscz.project.tools.aba.service.AbaPlanProjectService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;


@Service
public class AbaPlanProjectServiceImpl extends ServiceImpl<AbaPlanProjectMapper, AbaPlanProject> implements AbaPlanProjectService {

    @Resource
    private AbaPlanProjectMapper abaPlanProjectMapper;

    @Resource
    private AbaPlanProjectGoalMapper abaPlanProjectGoalMapper;

    /**
     * 获取计划项目列表(只包含进行中的)
     *
     * @param planId 计划ID
     * @return List<AbaPlanProject>
     */
    @Override
    public List<AbaPlanProject> getProgressListByPlanId (Integer planId) {
        LambdaQueryWrapper<AbaPlanProject> abaPlanProjectLambdaQueryWrapper = new LambdaQueryWrapper<AbaPlanProject>()
            .eq(AbaPlanProject::getPlanId, planId)
            .eq(AbaPlanProject::getStatus, 1)
            .orderByAsc(AbaPlanProject::getDomainId)
            .orderByAsc(AbaPlanProject::getStatus);
        List<AbaPlanProject> planProjectList = abaPlanProjectMapper.selectList(abaPlanProjectLambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(planProjectList)) {
            List<Integer> planProjectIds = planProjectList.stream().map(AbaPlanProject::getId).collect(Collectors.toList());
            LambdaQueryWrapper<AbaPlanProjectGoal> abaPlanProjectGoalLambdaQueryWrapper = new LambdaQueryWrapper<AbaPlanProjectGoal>()
                .eq(AbaPlanProjectGoal::getStatus, 1)
                .in(AbaPlanProjectGoal::getPlanProjectId, planProjectIds);
            List<AbaPlanProjectGoal> projectGoalList = abaPlanProjectGoalMapper.selectList(abaPlanProjectGoalLambdaQueryWrapper);
            planProjectList.forEach(project -> {
                List<AbaPlanProjectGoal> goalList = projectGoalList.stream().filter(goal -> goal.getPlanProjectId().equals(project.getId())).collect(Collectors.toList());
                project.setProjectGoalList(goalList);
            });
            planProjectList.removeIf(p->CollectionUtil.isEmpty(p.getProjectGoalList()));
        }
        return planProjectList;
    }
}
