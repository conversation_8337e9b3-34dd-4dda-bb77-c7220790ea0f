package com.songbai.qscz.project.tools.aba.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("aba_plan_project_goal_history")
public class AbaPlanProjectGoalHistory implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 机构ID
     */
    @TableField("org_id")
    private Integer orgId;

    /**
     * 儿童ID
     */
    @TableField("child_id")
    private Integer childId;

    /**
     * 计划ID
     */
    @TableField("plan_id")
    private Integer planId;

    /**
     * 领域ID
     */
    @TableField("domain_id")
    private Integer domainId;

    /**
     * 计划项目ID
     */
    @TableField("plan_project_id")
    private Integer planProjectId;

    /**
     * 项目ID
     */
    @TableField("project_id")
    private Integer projectId;

    /**
     * 项目名称
     */
    @TableField("project_name")
    private String projectName;

    /**
     * 计划项目目标ID
     */
    @TableField("plan_project_goal_id")
    private Integer planProjectGoalId;

    /**
     * 短期目标ID
     */
    @TableField("short_goal_id")
    private Integer shortGoalId;

    /**
     * 短期目标名称
     */
    @TableField("short_goal_name")
    private String shortGoalName;

    /**
     * 辅助方式
     */
    @TableField("assist_method")
    private String assistMethod;

    /**
     * 记录方式（1回合制 2试探 3分步骤 ）
     */
    @TableField("record_type")
    private Integer recordType;

    /**
     * 操作类型(0创建 1调整 2通过 3挂起)
     */
    @TableField("opt_type")
    private Integer optType;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 挂起原因
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 目标(Json格式存储)
     */
    @TableField("target")
    private String target;

    /**
     * 修改批次
     */
    @TableField("batch_no")
    private Integer batchNo;

    /**
     * 操作用户类型(1老师 2督导)
     */
    @TableField("user_type")
    private Integer userType;

    /**
     * 督导是否确认试探通过 1 已知晓 0 未知晓
     */
    @TableField("is_confirm")
    private Integer isConfirm;

    /**
     * 确认时间
     */
    @TableField("confirm_time")
    private Date confirmTime;

    /**
     * 确认人
     */
    @TableField("confirm_user")
    private String confirmUser;

    private static final long serialVersionUID = 1L;

    /**
     * 项目等级
     */
    @TableField(exist = false)
    private Integer projectLevel;
}
