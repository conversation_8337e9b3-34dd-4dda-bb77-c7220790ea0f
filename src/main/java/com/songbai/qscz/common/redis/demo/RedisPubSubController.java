package com.songbai.qscz.common.redis.demo;

import com.songbai.qscz.common.core.base.Result;
import com.songbai.qscz.common.redis.utils.RedisUtils;
import com.songbai.qscz.common.util.ResultUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * Redis 发布订阅 演示案例
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
// @RestController
// @RequestMapping("/demo/redis/pubsub")
public class RedisPubSubController {

    /**
     * 发布消息
     *
     * @param key   通道Key
     * @param value 发送内容
     */
    @GetMapping("/pub")
    public Result<String> pub(String key, String value) {
        RedisUtils.publish(key, value, consumer -> {
            System.out.println("发布通道 => " + key + ", 发送值 => " + value);
        });
        return ResultUtil.success("操作成功");
    }

    /**
     * 订阅消息
     *
     * @param key 通道Key
     */
    @GetMapping("/sub")
    public Result<String> sub(String key) {
        RedisUtils.subscribe(key, String.class, msg -> {
            System.out.println("订阅通道 => " + key + ", 接收值 => " + msg);
        });
        return ResultUtil.success("操作成功");
    }

}
