package com.songbai.qscz.common.redis.handler;

import cn.hutool.http.HttpStatus;
import com.baomidou.lock.exception.LockFailureException;
import com.songbai.qscz.common.core.base.Result;
import com.songbai.qscz.common.util.ResultUtil;
import com.songbai.qscz.common.web.util.RequestParamExtractor;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;


/**
 * Redis异常处理器
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class RedisExceptionHandler {

    /**
     * 分布式锁Lock4j异常
     */
    @ExceptionHandler(LockFailureException.class)
    public Result<Void> handleLockFailureException(LockFailureException e, HttpServletRequest request) {
        logExceptionDetail (request, e, "Lock4j异常");
        return ResultUtil.error (HttpStatus.HTTP_UNAVAILABLE, "业务处理中，请稍后再试...");
    }

    /**
     * 打印详细异常信息
     *
     * @param request
     * @param e
     * @param title
     */
    private void logExceptionDetail (HttpServletRequest request, Exception e, String title) {
        String requestURI = request.getRequestURI ();
        log.error ("********** {} 异常开始 **********", title);
        log.error ("请求地址: {}", requestURI);
        log.error ("请求URL: {}", request.getRequestURL ());
        String paramInfo = RequestParamExtractor.extractParameters (request);
        log.error ("请求参数: {}", paramInfo);
        log.error ("异常信息: {}", e.getMessage (), e);
        for (StackTraceElement element : e.getStackTrace ()) {
            log.error ("    {}", element.toString ());
        }
        log.error ("********** {} 异常结束 **********", title);
    }

}
