package com.songbai.qscz.common.redis.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 接收redis的消息通知
 * @since 2019/3/30 10:28
 */
@Slf4j
@Component
@Lazy
public class RedisMessageReceiverHandler {
    public void receiveMessage (String message) {
        System.out.println (message);
        log.info ("Redis key " + message + " Expired");
    }
}
