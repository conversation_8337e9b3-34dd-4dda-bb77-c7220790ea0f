package com.songbai.qscz.common.encrypt.config;

import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import com.songbai.qscz.common.encrypt.config.properties.EncryptorProperties;
import com.songbai.qscz.common.encrypt.core.EncryptorManager;
import com.songbai.qscz.common.encrypt.interceptor.MybatisDecryptInterceptor;
import com.songbai.qscz.common.encrypt.interceptor.MybatisEncryptInterceptor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 加解密配置
 *
 * <AUTHOR>
 * @version 4.6.0
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(EncryptorProperties.class)
@ConditionalOnProperty(value = "mybatis-encryptor.enable", havingValue = "true")
public class EncryptorConfiguration {

    @Resource
    private EncryptorProperties properties;

    @Bean
    public EncryptorManager encryptorManager(MybatisPlusProperties mybatisPlusProperties) {
        return new EncryptorManager(mybatisPlusProperties.getTypeAliasesPackage());
    }

    @Bean
    public MybatisEncryptInterceptor mybatisEncryptInterceptor(EncryptorManager encryptorManager) {
        return new MybatisEncryptInterceptor(encryptorManager, properties);
    }

    @Bean
    public MybatisDecryptInterceptor mybatisDecryptInterceptor(EncryptorManager encryptorManager) {
        return new MybatisDecryptInterceptor(encryptorManager, properties);
    }

}



