package com.songbai.qscz.common.util;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;

/**
 * <AUTHOR>
 * @description 订单号工具类
 * @since 2019/10/21 11:12
 */
public class TradeNoUtil {

    public static String createTKNo(String pre){
        StringBuilder sb = new StringBuilder (pre);
        ZonedDateTime now = ZonedDateTime.of (LocalDateTime.now (), ZoneId.of ("Asia/Shanghai"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        sb.append (now.format (formatter));
        sb.append (new Random ().nextInt(9000) + 1000);
        return sb.toString ();
    }
}
