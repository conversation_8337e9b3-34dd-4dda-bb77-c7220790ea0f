package com.songbai.qscz.common.ai.graph;



import java.util.List;

/**
 * 记录某会话Id的上下文（如线程ID等信息）
 *
 * <AUTHOR>
 * @since 2025/8/6
 */
public interface SessionContextService {

	void addSessionHistory(GraphId graphId, SessionHistory sessionHistory);

	List<String> getGraphThreadIds(String sessionId);

	List<SessionHistory> getReports(String sessionId, List<String> threadIds);

	List<SessionHistory> getRecentReports(String sessionId, int count);

	default List<SessionHistory> getRecentReports(String sessionId) {
		return this.getRecentReports(sessionId, 5);
	}

}
