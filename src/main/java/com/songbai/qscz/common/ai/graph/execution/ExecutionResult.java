/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.songbai.qscz.common.ai.graph.execution;

import com.songbai.qscz.common.ai.graph.GraphId;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 图执行结果封装类
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2025/8/25
 */
public class ExecutionResult {

    private final GraphId graphId;
    private final ExecutionStatus status;
    private final Map<String, Object> resultData;
    private final List<String> errors;
    private final long executionTimeMs;
    private final LocalDateTime startTime;
    private final LocalDateTime endTime;
    private final String executionNode;

    private ExecutionResult(Builder builder) {
        this.graphId = builder.graphId;
        this.status = builder.status;
        this.resultData = Collections.unmodifiableMap(new HashMap<>(builder.resultData));
        this.errors = Collections.unmodifiableList(new ArrayList<>(builder.errors));
        this.executionTimeMs = builder.executionTimeMs;
        this.startTime = builder.startTime;
        this.endTime = builder.endTime;
        this.executionNode = builder.executionNode;
    }

    /**
     * 检查执行是否成功
     * @return 成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return status == ExecutionStatus.COMPLETED && errors.isEmpty();
    }

    /**
     * 检查执行是否失败
     * @return 失败返回true，否则返回false
     */
    public boolean isFailure() {
        return status == ExecutionStatus.FAILED || !errors.isEmpty();
    }

    /**
     * 检查执行是否被取消
     * @return 取消返回true，否则返回false
     */
    public boolean isCancelled() {
        return status == ExecutionStatus.CANCELLED;
    }

    /**
     * 检查执行是否超时
     * @return 超时返回true，否则返回false
     */
    public boolean isTimeout() {
        return status == ExecutionStatus.TIMEOUT;
    }

    /**
     * 获取指定键的结果数据
     * @param key 数据键
     * @return 结果数据，不存在返回null
     */
    public Object getResult(String key) {
        return resultData.get(key);
    }

    /**
     * 获取指定键的结果数据，并转换为指定类型
     * @param key 数据键
     * @param clazz 目标类型
     * @param <T> 类型参数
     * @return 转换后的结果数据
     * @throws ClassCastException 如果类型转换失败
     */
    @SuppressWarnings("unchecked")
    public <T> T getResult(String key, Class<T> clazz) {
        Object value = resultData.get(key);
        if (value == null) {
            return null;
        }
        return (T) value;
    }

    /**
     * 获取所有结果数据
     * @return 不可修改的结果数据映射
     */
    public Map<String, Object> getAllResults() {
        return resultData;
    }

    /**
     * 获取所有错误信息
     * @return 不可修改的错误列表
     */
    public List<String> getErrors() {
        return errors;
    }

    /**
     * 获取第一个错误信息
     * @return 第一个错误信息，无错误返回null
     */
    public String getFirstError() {
        return errors.isEmpty() ? null : errors.get(0);
    }

    /**
     * 获取错误信息摘要
     * @return 错误摘要字符串
     */
    public String getErrorSummary() {
        if (errors.isEmpty()) {
            return "No errors";
        }
        return String.format("Total %d errors: %s", errors.size(), String.join("; ", errors));
    }

    // Getters
    public GraphId getGraphId() {
        return graphId;
    }

    public ExecutionStatus getStatus() {
        return status;
    }

    public long getExecutionTimeMs() {
        return executionTimeMs;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public String getExecutionNode() {
        return executionNode;
    }

    @Override
    public String toString() {
        return String.format("ExecutionResult{graphId=%s, status=%s, executionTimeMs=%d, errors=%d, results=%d}",
                graphId, status, executionTimeMs, errors.size(), resultData.size());
    }

    /**
     * 创建Builder实例
     * @param graphId 图ID
     * @return Builder实例
     */
    public static Builder builder(GraphId graphId) {
        return new Builder(graphId);
    }

    /**
     * 创建成功结果的快捷方法
     * @param graphId 图ID
     * @param resultData 结果数据
     * @param executionTimeMs 执行时间
     * @return 成功的执行结果
     */
    public static ExecutionResult success(GraphId graphId, Map<String, Object> resultData, long executionTimeMs) {
        return builder(graphId)
                .status(ExecutionStatus.COMPLETED)
                .resultData(resultData)
                .executionTimeMs(executionTimeMs)
                .build();
    }

    /**
     * 创建失败结果的快捷方法
     * @param graphId 图ID
     * @param error 错误信息
     * @param executionTimeMs 执行时间
     * @return 失败的执行结果
     */
    public static ExecutionResult failure(GraphId graphId, String error, long executionTimeMs) {
        return builder(graphId)
                .status(ExecutionStatus.FAILED)
                .addError(error)
                .executionTimeMs(executionTimeMs)
                .build();
    }

    /**
     * ExecutionResult构建器
     */
    public static class Builder {
        private final GraphId graphId;
        private ExecutionStatus status = ExecutionStatus.PENDING;
        private final Map<String, Object> resultData = new HashMap<>();
        private final List<String> errors = new ArrayList<>();
        private long executionTimeMs = 0;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private String executionNode;

        private Builder(GraphId graphId) {
            this.graphId = Objects.requireNonNull(graphId, "GraphId cannot be null");
        }

        public Builder status(ExecutionStatus status) {
            this.status = Objects.requireNonNull(status, "Status cannot be null");
            return this;
        }

        public Builder addResult(String key, Object value) {
            this.resultData.put(key, value);
            return this;
        }

        public Builder resultData(Map<String, Object> resultData) {
            if (resultData != null) {
                this.resultData.putAll(resultData);
            }
            return this;
        }

        public Builder addError(String error) {
            if (error != null && !error.trim().isEmpty()) {
                this.errors.add(error.trim());
            }
            return this;
        }

        public Builder addErrors(List<String> errors) {
            if (errors != null) {
                errors.stream()
                        .filter(error -> error != null && !error.trim().isEmpty())
                        .forEach(error -> this.errors.add(error.trim()));
            }
            return this;
        }

        public Builder executionTimeMs(long executionTimeMs) {
            this.executionTimeMs = Math.max(0, executionTimeMs);
            return this;
        }

        public Builder startTime(LocalDateTime startTime) {
            this.startTime = startTime;
            return this;
        }

        public Builder endTime(LocalDateTime endTime) {
            this.endTime = endTime;
            return this;
        }

        public Builder executionNode(String executionNode) {
            this.executionNode = executionNode;
            return this;
        }

        public ExecutionResult build() {
            // 自动计算执行时间
            if (startTime != null && endTime != null && executionTimeMs == 0) {
                this.executionTimeMs = java.time.Duration.between(startTime, endTime).toMillis();
            }
            
            return new ExecutionResult(this);
        }
    }
}
