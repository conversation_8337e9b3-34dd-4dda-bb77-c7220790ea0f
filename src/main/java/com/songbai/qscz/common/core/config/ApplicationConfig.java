package com.songbai.qscz.common.core.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 程序注解配置
 *
 * <AUTHOR> Li
 */
@Configuration
@EnableRetry
@EnableScheduling
@EnableAspectJAutoProxy
public class ApplicationConfig {

}
