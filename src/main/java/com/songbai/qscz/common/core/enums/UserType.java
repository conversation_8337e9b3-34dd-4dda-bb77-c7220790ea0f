package com.songbai.qscz.common.core.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户类型
 *
 * <AUTHOR> Li
 */
@Getter
@AllArgsConstructor
public enum UserType {

    /**
     * 青松成长用户
     */
    QSCZ_USER("qscz_user"),

    /**
     * ERP用户
     */
    ERP_USER("erp_user"),

    /**
     * ERP用户
     */
    USER_CENTER ("user_center_user");

    /**
     * 用户类型标识（用于 token、权限识别等）
     */
    private final String userType;

    public static UserType getUserType(String str) {
        for (UserType value : values()) {
            if (StrUtil.contains(str, value.getUserType())) {
                return value;
            }
        }
        throw new RuntimeException("'UserType' not found By " + str);
    }
}
