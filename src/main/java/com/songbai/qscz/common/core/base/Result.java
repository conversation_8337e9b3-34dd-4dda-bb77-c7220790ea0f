package com.songbai.qscz.common.core.base;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Result<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 消息
     */
    private String message;

    /**
     * 数据
     */
    private T data;

    /**
     * 添加多个返回值
     * @param key key
     * @param value value
     * @return
     */
    public Result<T> putMultipleData(String key, Object value) {
        if(this.data instanceof Map){
            ((Map<String, Object>) this.data).put (key, value);
        }
        return this;
    }

}
