package com.songbai.qscz.common.core.constant;

/**
 * ERP系统常量
 *
 * <AUTHOR>
 * @since 2025-06-10 14:00
 */
public class ErpConstants {

    public static final int COMM_STATUS_DELETE = -1; // 通用状态常量，删除
    public static final int COMM_STATUS_OFF = 0; // 通用状态常量， 关闭
    public static final int COMM_STATUS_ON = 1; // 通用状态常量，开启
    public static final int ADULT_AGE = 15; //成年人年龄
    public static final int ROLE_ID_REGISTER = 23; // 注册用户默认角色

    /**
     * 验证信息错误记录
     */
    public final static String CAPTCHA_IMAGECODE_GET = "获取图片验证码失败";
    public final static String CAPTCHA_IMAGECODE_CHECK_NO = "验证码已失效，请重新输入！";
    public final static String CAPTCHA_IMAGECODE_CHECK_FALSE = "验证码错误！";

    /**
     * 获取微信用户信息时的地区语言版本
     */
    public final static String ZH_CN = "zh_CN";//中文简体
    public final static String ZH_TW = "zh_TW";//中文繁体
    public final static String EN = "en";//英语

    /**
     * 用户类型
     */
    public final static Integer PATIENT = 1;
    public final static Integer DOCTOR = 2;
    public final static Integer MECHEMP = 4;
    public final static Integer RELATIVE = 5;

    /**
     * 用户性别-男1
     */
    public final static Integer USER_GENDER_MAN = 1;
    /**
     * 用户性别-女0
     */
    public final static Integer USER_GENDER_WOMAN = 0;



    /**
     * 医生端工作台首页红点redis的key(key为此值 + 医生id)
     */
    public final static String RED_DOT = "redDotMap";


    /**
     * 收支类型-收入1
     */
    public final static int AMOUNT_TYPE_INCOME = 1;
    /**
     * 收支类型-支出2
     */
    public final static int AMOUNT_TYPE_OUTTO = 2;

    /**
     * 支付方式-微信1
     */
    public final static int PAY_TYPE_WECHAT = 1;
    /**
     * 支付方式-支付宝2
     */
    public final static int PAY_TYPE_ALIPAY = 2;
    /**
     * 支付方式-银行3
     */
    public final static int PAY_TYPE_BANK = 3;
    /**
     * 支付方式-现金4
     */
    public final static int PAY_TYPE_CASH = 4;
    /**
     * 支付方式-慈善基金5
     */
    public final static int PAY_TYPE_CHARITY = 5;
    /**
     * 支付方式-其他3
     */
    public final static int PAY_TYPE_OTHER = 6;

    /**
     * 业务类型-充值1
     */
    public final static int BIZ_TYPE_RECHARGE = 1;
    /**
     * 业务类型-评估扣款2
     */
    public final static int BIZ_TYPE_ASSESS_CUT = 2;
    /**
     * 业务类型-康复任务扣款3
     */
    public final static int BIZ_TYPE_TASK_CUT = 3;
    /**
     * 业务类型-提现4
     */
    public final static int BIZ_TYPE_WITHDRAW = 4;
    /**
     * 业务类型-评估退款5
     */
    public final static int BIZ_TYPE_ASSESS_REFUND = 5;
    /**
     * 业务类型-任务退款6
     */
    public final static int BIZ_TYPE_TASK_REFUND = 6;
    /**
     * 业务类型-评估取消7
     */
    public final static int BIZ_TYPE_ASSESS_CANCEL = 7;
    /**
     * 业务类型-任务取消8
     */
    public final static int BIZ_TYPE_TASK_CANCEL = 8;


    /**
     * 服务群体-儿童1
     */
    public final static int SERVICE_GROUP_CHILDREN = 1;
    /**
     * 服务群体-成人2
     */
    public final static int SERVICE_GROUP_AUDIT = 2;



    /**
     * 服务方式-到店1
     */
    public final static int SERVICE_SERVICETYPE_TOSHOP = 1;
    /**
     * 服务方式-上门2
     */
    public final static int SERVICE_SERVICETYPE_TOVISIT = 2;

    /**
     * 服务时长-20分钟/节3
     */
    public final static int SERVICE_PERIOD_20 = 3;
    /**
     * 服务时长-30分钟/节1
     */
    public final static int SERVICE_PERIOD_30 = 1;
    /**
     * 服务时长-50分钟/节2
     */
    public final static int SERVICE_PERIOD_50 = 2;
}
