package com.songbai.qscz.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 登录类型
 *
 * <AUTHOR> Li
 */
@Getter
@AllArgsConstructor
public enum LoginType {

    /**
     * 密码登录
     */
    PASSWORD(1,"密码输入错误超过{}次，帐户锁定{}分钟", "密码输入错误{}次"),

    /**
     * 短信登录
     */
    SMS(2,"短信验证码输入错误超过{}次，帐户锁定{}分钟", "短信验证码输入错误{}次"),

    /**
     * 微信登录
     */
    WX(3,"", "");

    /**
     * 登录类型代码
     */
    final Integer loginTypeCode;

    /**
     * 登录重试超出限制提示
     */
    final String retryLimitExceed;

    /**
     * 登录重试限制计数提示
     */
    final String retryLimitCount;
}
