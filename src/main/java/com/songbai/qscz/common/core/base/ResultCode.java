package com.songbai.qscz.common.core.base;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */

public enum ResultCode {
    // 200 正确
    // 400 参数错误
    // 401 未授权
    // 403 禁止访问
    // 404 资源未找到
    // 405 方法不允许
    // 500 服务器错误
    // 501 网络错误
    // 502 网络超时
    // 503 网络未授权
    // 50x 服务器错误


    // 成功
    SUCCESS (200, "成功"),
    // 失败
    FAIL (500, "失败"),
    // 未授权
    UNAUTHORIZED (401, ""),
    // 访问受限
    FORBIDDEN (403, "没有权限，请联系管理员授权"),
    // 接口不存在
    NOT_FOUND (404, "接口不存在"),
    // 服务器内部错误
    INTERNAL_SERVER_ERROR (500, "服务器内部错误，请联系管理员"),
    // 未知错误
    UNKNOWN_ERROR (500, "未知错误，请联系管理员"),

    // 登录二维码已失效
    QRCODE_IS_INVALID (10001, "登录二维码已失效"),
    // 等待扫码登录
    WAIT_SCAN (10002, "等待扫码登录"),
    // 已扫码未登录
    SCANNED_NOLOGIN (10003, "已扫码未登录"),
    // openId未绑定用户
    OPENID_NOT_BIND_USER (10004, "openId未绑定用户"),
    /* 参数错误：10001-19999 */
    PARAM_IS_INVALID(10001, "参数无效"), PARAM_IS_BLANK(10002, "参数为空"), PARAM_TYPE_BIND_ERROR(10003,
        "参数类型错误"), PARAM_NOT_COMPLETE(10004, "参数缺失"),

    /* 用户错误：20001-29999 */
    USER_NOT_LOGGED_IN(20001, "用户未登录"), USER_LOGIN_ERROR(20002, "账号不存在或密码错误"), USER_ACCOUNT_FORBIDDEN(20003,
        "账号已被禁用"), USER_NOT_EXIST(20004, "用户不存在"), USER_HAS_EXISTED(20005, "用户已存在"), MSG_IS_ERROR(20006,
        "验证码错误"), TIME_IS_OUT(20007, "验证码超时"), MOBILE_IS_EXISTED(20008, "该手机号已注册");


    @Getter
    @Setter
    private Integer code;
    @Getter
    @Setter
    private String msg;


    ResultCode (Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}
