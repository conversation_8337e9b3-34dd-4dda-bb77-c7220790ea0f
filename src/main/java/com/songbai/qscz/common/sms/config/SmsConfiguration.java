package com.songbai.qscz.common.sms.config;


import com.songbai.qscz.common.sms.core.dao.PlusSmsDao;
import com.songbai.qscz.common.sms.handler.SmsExceptionHandler;
import org.dromara.sms4j.api.dao.SmsDao;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 短信配置类
 *
 * <AUTHOR>
 */
@Configuration
public class SmsConfiguration {

    @Primary
    @Bean
    public SmsDao smsDao() {
        return new PlusSmsDao ();
    }

    /**
     * 异常处理器
     */
    @Bean
    public SmsExceptionHandler smsExceptionHandler() {
        return new SmsExceptionHandler();
    }

}
