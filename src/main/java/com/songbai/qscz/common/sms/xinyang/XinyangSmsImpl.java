package com.songbai.qscz.common.sms.xinyang;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sms4j.api.entity.SmsResponse;
import org.dromara.sms4j.comm.delayedTime.DelayedTime;
import org.dromara.sms4j.comm.exception.SmsBlendException;
import org.dromara.sms4j.provider.service.AbstractSmsBlend;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Executor;

/**
 * 歆阳短信实现
 *
 * <AUTHOR>
 * @since 2023/10/9 14:04
 */
@Slf4j
public class XinyangSmsImpl extends AbstractSmsBlend<XinyangConfig> {

    private int retry = 0;

    /**
     * 构造器，用于构造短信实现模块
     * @param config
     * @param pool
     * @param delayedTime
     */
    public XinyangSmsImpl(XinyangConfig config, Executor pool, DelayedTime delayedTime) {
        super(config, pool, delayedTime);
    }

    /**
     * 构造器，用于构造短信实现模块
     * @param config
     */
    protected XinyangSmsImpl (XinyangConfig config) {
        super (config);
    }

    @Override
    public String getSupplier () {
        return "xinyang";
    }

    /**
     * <p>说明：发送固定消息模板短信
     * <p>此方法将使用配置文件中预设的短信模板进行短信发送
     * <p>该方法指定的模板变量只能存在一个（配置文件中）
     * <p>如使用的是腾讯的短信，参数字符串中可以同时存在多个参数，使用 & 分隔例如：您的验证码为{1}在{2}分钟内有效，可以传为  message="xxxx"+"&"+"5"
     * sendMessage
     *
     * @param phone   接收短信的手机号
     *                message 消息内容
     * @param message
     * <AUTHOR>
     */
    @Override
    public SmsResponse sendMessage (String phone, String message) {
        Optional.ofNullable(phone).orElseThrow(() -> new SmsBlendException ("手机号不能为空"));
        return getSmsResponse(getConfig ().getUrl (), phone, message);
    }

    /**
     * sendMessage
     * <p>说明：发送固定消息模板多模板参数短信
     *
     * @param phone    接收短信的手机号
     * @param messages 模板内容
     * <AUTHOR>
     */
    @Override
    public SmsResponse sendMessage (String phone, LinkedHashMap<String, String> messages) {
        return null;
    }

    /**
     * <p>说明：使用自定义模板发送短信
     * sendMessage
     *
     * @param phone
     * @param templateId 模板id
     * @param messages   key为模板变量名称 value为模板变量值
     * <AUTHOR>
     */
    @Override
    public SmsResponse sendMessage (String phone, String templateId, LinkedHashMap<String, String> messages) {
        return null;
    }

    /**
     * <p>说明：群发固定模板短信
     * massTexting
     *
     * @param phones
     * @param message
     * <AUTHOR>
     */
    @Override
    public SmsResponse massTexting (List<String> phones, String message) {
        return null;
    }

    /**
     * <p>说明：使用自定义模板群发短信
     * massTexting
     *
     * @param phones
     * @param templateId
     * @param messages
     * <AUTHOR>
     */
    @Override
    public SmsResponse massTexting (List<String> phones, String templateId, LinkedHashMap<String, String> messages) {
        return null;
    }

    private SmsResponse getSmsResponse(String requestUrl,String phone, String message) {
        HashMap<String, Object> paramMap = new HashMap<>(8);
        paramMap.put("action", "send");
        paramMap.put("userid", getConfig ().getSdkAppId ());
        paramMap.put("account", getConfig ().getAccessKeyId ());
        paramMap.put("password", getConfig ().getAccessKeySecret ());
        paramMap.put("mobile", phone);
        paramMap.put("content", getConfig().getSignature () + message);
        paramMap.put("sendTime", "");
        paramMap.put("extno", "");

        try {
            String retStr = HttpRequest.post (requestUrl)
                    .header (Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                    .header (Header.CONNECTION, "Keep-Alive")
                    .header (Header.ACCEPT_CHARSET, "UTF-8")
                    .form(paramMap)
                    .execute().body();
            SmsResponse smsResponse = getResponse(retStr);
            if(smsResponse.isSuccess() || retry == getConfig().getMaxRetries()){
                retry = 0;
                return smsResponse;
            }
            return requestRetry(requestUrl, phone, message);
        }catch (SmsBlendException e){
            return requestRetry(requestUrl, phone, message);
        }
    }

    private SmsResponse requestRetry(String requestUrl,String phone, String message) {
        http.safeSleep(getConfig().getRetryInterval());
        retry++;
        log.warn("短信第 {" + retry + "} 次重新发送");
        return getSmsResponse(requestUrl, phone, message);
    }

    private SmsResponse getResponse(String resStr) {
        SmsResponse smsResponse = new SmsResponse();
        JSONObject json = JSONUtil.parseFromXml (resStr);
        smsResponse.setSuccess("Success".equals (json.getJSONObject ("returnsms").getStr ("returnstatus")));
        smsResponse.setData(json);
        smsResponse.setConfigId(getConfigId());
        return smsResponse;
    }
}
