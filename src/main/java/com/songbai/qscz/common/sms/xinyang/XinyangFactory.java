package com.songbai.qscz.common.sms.xinyang;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.dromara.sms4j.provider.factory.AbstractProviderFactory;

/**
 * 歆阳对象构造者
 *
 * <AUTHOR>
 * @since 2023/10/9 14:25
 */
@NoArgsConstructor (access = AccessLevel.PRIVATE)
public class XinyangFactory extends AbstractProviderFactory<XinyangSmsImpl, XinyangConfig> {

    private static final XinyangFactory INSTANCE = new XinyangFactory();

    /**
     * 获取建造者实例
     * @return 建造者实例
     */
    public static XinyangFactory instance() {
        return INSTANCE;
    }

    /**
     * 创建短信实现对象
     *
     * @param xinyangConfig 短信配置对象
     * @return 短信实现对象
     */
    @Override
    public XinyangSmsImpl createSms (XinyangConfig xinyangConfig) {
        return new XinyangSmsImpl (xinyangConfig);
    }

    /**
     * 获取供应商
     *
     * @return 供应商
     */
    @Override
    public String getSupplier () {
        return "xinyang";
    }
}
