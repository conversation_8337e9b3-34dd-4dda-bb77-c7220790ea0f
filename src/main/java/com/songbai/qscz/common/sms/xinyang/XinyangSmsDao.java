package com.songbai.qscz.common.sms.xinyang;

import com.songbai.qscz.common.redis.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sms4j.api.dao.SmsDao;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * 歆阳短信缓存配置
 *
 * <AUTHOR>
 * @since 2023/10/9 15:38
 */
@Slf4j
@Component
public class XinyangSmsDao implements SmsDao {

    private static String PREFIX = "sms_xinyang_";

    @Override
    public void set(String key, Object value, long cacheTime) {
        RedisUtils.setCacheObject(PREFIX + key, value, Duration.ofSeconds(cacheTime));
    }

    @Override
    public void set(String key, Object value) {
        this.set(PREFIX + key, value, 86400000L);
    }

    @Override
    public Object get(String key) {
        return RedisUtils.getCacheObject (PREFIX + key);
    }

    /**
     * remove
     * <p> 根据key移除缓存
     *
     * @param key 缓存键
     * @return 被删除的value
     * <AUTHOR>
     */
    @Override
    public Object remove (String key) {
        Object o = RedisUtils.getCacheObject (PREFIX + key);
        RedisUtils.deleteObject (PREFIX + key);
        return o;
    }

    @Override
    public void clean() {
        RedisUtils.deleteKeys (PREFIX+"*");
    }
}
