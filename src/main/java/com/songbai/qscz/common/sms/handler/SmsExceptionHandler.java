package com.songbai.qscz.common.sms.handler;

import cn.hutool.http.HttpStatus;
import com.songbai.qscz.common.core.base.Result;
import com.songbai.qscz.common.util.ResultUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sms4j.comm.exception.SmsBlendException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * SMS异常处理器
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class SmsExceptionHandler {

    /**
     * sms异常
     */
    @ExceptionHandler(SmsBlendException.class)
    public Result<Void> handleSmsBlendException(SmsBlendException e, HttpServletRequest request) {
        if(e.getMessage ().contains ("number of short messages reached the maximum today")){
            return ResultUtil.error ("今日验证码发送次数达到上限");
        } else if (e.getMessage ().contains ("Text messages are sent too often")) {
            return ResultUtil.error ("请勿频繁发送验证码，请稍后再试...");
        } else {
            String requestURI = request.getRequestURI();
            log.error("请求地址'{}',短信异常.", requestURI, e);
            return ResultUtil.error (HttpStatus.HTTP_INTERNAL_ERROR, "短信发送失败，请稍后再试...");
        }
    }

}
