package com.songbai.qscz.common.sms.xinyang;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.sms4j.provider.config.BaseConfig;

/**
 * 歆阳短信配置
 *
 * <AUTHOR>
 * @since 2023/10/9 14:05
 */
@Data
@EqualsAndHashCode (callSuper = true)
public class XinyangConfig extends BaseConfig {

    private String configId = "xinyang";
    private String supplier = "xinyang";

    /**
     * 接口地址
     */
    private String url = "http://sdk2.yd10086.cn:8888/sms.aspx";

    @Override
    public String getConfigId() {
        return configId;
    }

    @Override
    public String getSupplier() {
        return supplier;
    }
}
