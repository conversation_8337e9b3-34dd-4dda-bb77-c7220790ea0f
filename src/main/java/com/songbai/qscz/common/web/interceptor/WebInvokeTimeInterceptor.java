package com.songbai.qscz.common.web.interceptor;

import cn.hutool.core.util.ObjectUtil;
import com.songbai.qscz.common.web.config.properties.WebLogProperties;
import com.songbai.qscz.common.web.util.RequestParamExtractor;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * web的调用时间统计拦截器 - 性能优化版
 *
 * <AUTHOR>
 */
@Slf4j
public class WebInvokeTimeInterceptor implements HandlerInterceptor {


    private WebLogProperties webLogProperties;

    private final static ThreadLocal<StopWatch> KEY_CACHE = new ThreadLocal<> ();

    /**
     * 需要跳过参数记录的URL模式
     */
    private static final Set<Pattern> SKIP_PARAM_PATTERNS = ConcurrentHashMap.newKeySet ();

    static {
        // 静态资源和健康检查等不需要记录参数的URL
        SKIP_PARAM_PATTERNS.add (Pattern.compile (".*/actuator/.*"));
        SKIP_PARAM_PATTERNS.add (Pattern.compile (".*\\.(js|css|png|jpg|jpeg|gif|ico|woff|woff2|ttf|svg)$"));
        SKIP_PARAM_PATTERNS.add (Pattern.compile (".*/health.*"));
    }

    public WebInvokeTimeInterceptor (WebLogProperties webLogProperties) {
        this.webLogProperties = webLogProperties;
    }

    @Override
    public boolean preHandle (HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (! webLogProperties.getEnablePerformance ()) {
            return true;
        }

        String url = request.getMethod () + " " + request.getRequestURI ();

        // 启动性能计时
        StopWatch stopWatch = new StopWatch ();
        KEY_CACHE.set (stopWatch);
        stopWatch.start ();

        // 只在启用详细参数记录时才记录参数
        if (webLogProperties.getEnableDetailParams () && ! shouldSkipParamLogging (request.getRequestURI ())) {
            try {
                String paramInfo = RequestParamExtractor.extractParameters (request);
                log.info ("[QSCZ]开始请求 => URL[{}], {}", url, paramInfo);
            } catch (Exception e) {
                log.warn ("[QSCZ]记录请求参数失败 => URL[{}], 错误信息:[{}]", url, e.getMessage ());
            }
        } else {
            // 简化日志，只记录URL
            log.info ("[QSCZ]开始请求 => URL[{}]", url);
        }

        return true;
    }

    @Override
    public void postHandle (HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // 空实现，减少不必要的处理
    }

    @Override
    public void afterCompletion (HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        if (! webLogProperties.getEnablePerformance ()) {
            return;
        }

        StopWatch stopWatch = KEY_CACHE.get ();
        if (ObjectUtil.isNotNull (stopWatch)) {
            stopWatch.stop ();
            long duration = stopWatch.getDuration ().toMillis ();
            String url = request.getMethod () + " " + request.getRequestURI ();

            // 根据请求耗时决定日志级别
            if (duration > webLogProperties.getSlowRequestThreshold ()) {
                log.warn ("[QSCZ]慢请求 => URL[{}],耗时:[{}]毫秒", url, duration);
            } else if (webLogProperties.getEnableDetailParams ()) {
                log.info ("[QSCZ]结束请求 => URL[{}],耗时:[{}]毫秒", url, duration);
            } else {
                // 对于快速请求，使用debug级别
                log.debug ("[QSCZ]结束请求 => URL[{}],耗时:[{}]毫秒", url, duration);
            }

            KEY_CACHE.remove ();
        }
    }

    /**
     * 判断是否应该跳过参数记录
     */
    private boolean shouldSkipParamLogging (String uri) {
        return SKIP_PARAM_PATTERNS.stream ().anyMatch (pattern -> pattern.matcher (uri).matches ());
    }

}
