package com.songbai.qscz.common.web.convert;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.core.convert.converter.Converter;

import java.util.Date;

/**
 * 日期转换类
 * 将标准日期、标准日期时间、时间戳转换成Date类型
 * <AUTHOR>
 */
public class TimestampToDateConverter implements Converter<String, Date> {

    private static final String timeStampFormat = "^\\d+$";

    @Override
    public Date convert(String param) {
        if(StrUtil.isEmpty (param)) {
            return null;
        }
        param = param.trim();

        try {
            if (param.matches(timeStampFormat)) {
                return new Date(Long.parseLong (param));
            } else {
                return DateUtil.parse (param);
            }
        } catch (Exception e) {
            throw new RuntimeException(String.format("parser %s to Date fail", param));
        }
    }
}
