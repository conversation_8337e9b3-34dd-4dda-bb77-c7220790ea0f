package com.songbai.qscz.common.web.handler;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpStatus;
import com.fasterxml.jackson.core.JsonParseException;
import com.songbai.qscz.common.core.base.Result;
import com.songbai.qscz.common.core.exception.CustomServiceException;
import com.songbai.qscz.common.util.ResultUtil;
import com.songbai.qscz.common.util.StreamUtils;
import com.songbai.qscz.common.web.util.RequestParamExtractor;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.io.IOException;


/**
 * 全局异常处理器
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler (HttpRequestMethodNotSupportedException.class)
    public Result<Void> handleHttpRequestMethodNotSupported (HttpRequestMethodNotSupportedException e, HttpServletRequest request) {
        logExceptionDetail (request, e, "请求方式不支持");
        return ResultUtil.error (HttpStatus.HTTP_BAD_METHOD, e.getMessage ());
    }

    @ExceptionHandler (CustomServiceException.class)
    public Result<Void> handleServiceException (CustomServiceException e, HttpServletRequest request) {
        logExceptionDetail (request, e, "业务异常");
        Integer code = e.getCode ();
        return ObjectUtil.isNotNull (code) ? ResultUtil.error (code, e.getMessage ()) : ResultUtil.error (e.getMessage ());
    }

    @ExceptionHandler (WxErrorException.class)
    public Result<?> handleWxErrorException (WxErrorException e, HttpServletRequest request) {
        logExceptionDetail (request, e, "微信接口异常");
        return ResultUtil.error (e.getMessage ());
    }

    @ExceptionHandler (ServletException.class)
    public Result<Void> handleServletException (ServletException e, HttpServletRequest request) {
        logExceptionDetail (request, e, "Servlet异常");
        return ResultUtil.error (e.getMessage ());
    }

    @ExceptionHandler (MissingPathVariableException.class)
    public Result<Void> handleMissingPathVariableException (MissingPathVariableException e, HttpServletRequest request) {
        logExceptionDetail (request, e, "路径变量缺失");
        return ResultUtil.error (String.format ("请求路径中缺少必需的路径变量[%s]", e.getVariableName ()));
    }

    @ExceptionHandler (MethodArgumentTypeMismatchException.class)
    public Result<Void> handleMethodArgumentTypeMismatchException (MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        logExceptionDetail (request, e, "参数类型不匹配");
        return ResultUtil.error (String.format ("参数[%s]类型不匹配，应为'%s'，但输入值为：'%s'",
            e.getName (), e.getRequiredType ().getName (), e.getValue ()));
    }

    @ExceptionHandler (NoHandlerFoundException.class)
    public Result<Void> handleNoHandlerFoundException (NoHandlerFoundException e, HttpServletRequest request) {
        logExceptionDetail (request, e, "请求地址不存在");
        return ResultUtil.error (HttpStatus.HTTP_NOT_FOUND, e.getMessage ());
    }

    @ResponseStatus (org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler (IOException.class)
    public void handleIOException (IOException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI ();
        if (requestURI.contains ("sse")) {
            // SSE 连接中断属于正常行为，静默处理
            return;
        }
        logExceptionDetail (request, e, "IO异常");
    }

    @ExceptionHandler (BindException.class)
    public Result<Void> handleBindException (BindException e, HttpServletRequest request) {
        logExceptionDetail (request, e, "参数绑定异常");
        String message = StreamUtils.join (e.getAllErrors (), DefaultMessageSourceResolvable::getDefaultMessage, ", ");
        return ResultUtil.error (message);
    }

    @ExceptionHandler (ConstraintViolationException.class)
    public Result<Void> handleConstraintViolationException (ConstraintViolationException e, HttpServletRequest request) {
        logExceptionDetail (request, e, "参数校验异常");
        String message = StreamUtils.join (e.getConstraintViolations (), ConstraintViolation::getMessage, ", ");
        return ResultUtil.error (message);
    }

    @ExceptionHandler (MethodArgumentNotValidException.class)
    public Result<Void> handleMethodArgumentNotValidException (MethodArgumentNotValidException e, HttpServletRequest request) {
        logExceptionDetail (request, e, "方法参数校验异常");
        String message = StreamUtils.join (e.getBindingResult ().getAllErrors (), DefaultMessageSourceResolvable::getDefaultMessage, ", ");
        return ResultUtil.error (message);
    }

    @ExceptionHandler (JsonParseException.class)
    public Result<Void> handleJsonParseException (JsonParseException e, HttpServletRequest request) {
        logExceptionDetail (request, e, "JSON解析异常");
        return ResultUtil.error (HttpStatus.HTTP_BAD_REQUEST, "请求数据格式错误（JSON 解析失败）：" + e.getMessage ());
    }

    @ExceptionHandler (HttpMessageNotReadableException.class)
    public Result<Void> handleHttpMessageNotReadableException (HttpMessageNotReadableException e, HttpServletRequest request) {
        logExceptionDetail (request, e, "请求体读取异常");
        return ResultUtil.error (HttpStatus.HTTP_BAD_REQUEST, "请求参数格式错误：" + e.getMostSpecificCause ().getMessage ());
    }

    @ExceptionHandler (RuntimeException.class)
    public Result<Void> handleRuntimeException (RuntimeException e, HttpServletRequest request) {
        return handleException (e, request);
    }

    @ExceptionHandler (Exception.class)
    public Result<Void> handleException (Exception e, HttpServletRequest request) {
        logExceptionDetail (request, e, "未知异常");
        return ResultUtil.error ("系统异常，请联系管理员！");
    }

    /**
     * 打印详细异常信息
     *
     * @param request
     * @param e
     * @param title
     */
    private void logExceptionDetail (HttpServletRequest request, Exception e, String title) {
        String requestURI = request.getRequestURI ();
        log.error ("********** {} 异常开始 **********", title);
        log.error ("请求地址: {}", requestURI);
        log.error ("请求URL: {}", request.getRequestURL ());
        String paramInfo = RequestParamExtractor.extractParameters (request);
        log.error ("请求参数: {}", paramInfo);
        log.error ("异常信息: {}", e.getMessage (), e);
        for (StackTraceElement element : e.getStackTrace ()) {
            log.error ("    {}", element.toString ());
        }
        log.error ("********** {} 异常结束 **********", title);
    }

}
