package com.songbai.qscz.common.web.filter;

import cn.hutool.core.io.IoUtil;
import com.songbai.qscz.common.core.constant.Constants;
import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * 构建可重复读取inputStream的request
 *
 * 注意事项：
 * 1. 会将整个请求体加载到内存中，大文件上传时需要注意内存使用
 * 2. 只适用于文本类型的请求体，二进制数据可能会有问题
 * 3. 需要配合Filter使用，确保在业务逻辑之前进行包装
 *
 * <AUTHOR>
 */
@Slf4j
public class RepeatedlyRequestWrapper extends HttpServletRequestWrapper {

    private final byte[] body;
    private final int maxBodySize;
    private final boolean isOversized;

    /**
     * 默认最大请求体大小：50MB
     */
    private static final int DEFAULT_MAX_BODY_SIZE = 50 * 1024 * 1024;

    public RepeatedlyRequestWrapper(HttpServletRequest request, ServletResponse response) throws IOException {
        this(request, response, DEFAULT_MAX_BODY_SIZE);
    }

    public RepeatedlyRequestWrapper(HttpServletRequest request, ServletResponse response, int maxBodySize) throws IOException {
        super(request);
        this.maxBodySize = maxBodySize;

        // 设置字符编码
        String encoding = request.getCharacterEncoding();
        if (encoding == null) {
            request.setCharacterEncoding(Constants.UTF8);
        }
        response.setCharacterEncoding(Constants.UTF8);

        // 检查Content-Length
        int contentLength = request.getContentLength();
        if (contentLength > maxBodySize) {
            log.warn("请求体过大，ContentLength: {}, 最大允许: {}, URI: {}", contentLength, maxBodySize, request.getRequestURI());
            this.isOversized = true;
            this.body = new byte[0];
            return;
        }

        // 读取请求体
        try {
            this.body = IoUtil.readBytes(request.getInputStream(), false);
            this.isOversized = false;

            if (this.body.length > maxBodySize) {
                log.warn("实际请求体大小超过限制，实际大小: {}, 最大允许: {}, URI: {}",
                    this.body.length, maxBodySize, request.getRequestURI());
            }
        } catch (IOException e) {
            log.error("读取请求体失败，URI: {}, 错误: {}", request.getRequestURI(), e.getMessage());
            throw e;
        }
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(getInputStream(), getCharacterEncodingOrDefault()));
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        if (isOversized) {
            throw new IOException("请求体过大，无法读取");
        }

        final ByteArrayInputStream bais = new ByteArrayInputStream(body);
        return new CachedBodyServletInputStream(bais, body.length);
    }

    /**
     * 获取请求体的字节数组
     *
     * @return 请求体字节数组
     */
    public byte[] getBody() {
        return body.clone(); // 返回副本，避免外部修改
    }

    /**
     * 获取请求体的字符串形式
     *
     * @return 请求体字符串
     */
    public String getBodyString() {
        return getBodyString(getCharacterEncodingOrDefault());
    }

    /**
     * 获取请求体的字符串形式（指定编码）
     *
     * @param charset 字符编码
     * @return 请求体字符串
     */
    public String getBodyString(String charset) {
        if (isOversized) {
            return "[请求体过大，无法显示]";
        }

        if (body == null || body.length == 0) {
            return "";
        }

        try {
            return new String(body, charset);
        } catch (Exception e) {
            log.warn("字符编码转换失败，使用UTF-8: {}", e.getMessage());
            return new String(body, StandardCharsets.UTF_8);
        }
    }

    /**
     * 检查请求体是否过大
     */
    public boolean isOversized() {
        return isOversized;
    }

    /**
     * 获取请求体大小
     */
    public int getBodySize() {
        return body != null ? body.length : 0;
    }

    /**
     * 获取字符编码，如果未设置则返回UTF-8
     */
    private String getCharacterEncodingOrDefault() {
        String encoding = getCharacterEncoding();
        return encoding != null ? encoding : Constants.UTF8;
    }

    /**
     * 自定义ServletInputStream实现
     */
    private static class CachedBodyServletInputStream extends ServletInputStream {
        private final ByteArrayInputStream inputStream;
        private final int totalSize;
        private int readCount = 0;

        public CachedBodyServletInputStream(ByteArrayInputStream inputStream, int totalSize) {
            this.inputStream = inputStream;
            this.totalSize = totalSize;
        }

        @Override
        public int read() throws IOException {
            int data = inputStream.read();
            if (data != -1) {
                readCount++;
            }
            return data;
        }

        @Override
        public int available() throws IOException {
            return inputStream.available();
        }

        @Override
        public boolean isFinished() {
            return readCount >= totalSize || inputStream.available() == 0;
        }

        @Override
        public boolean isReady() {
            // 数据已经在内存中，总是ready
            return true;
        }

        @Override
        public void setReadListener(ReadListener readListener) {
            // 同步读取，不需要监听器
            // 如果需要支持异步，可以在这里实现
        }

        @Override
        public int read(byte[] b, int off, int len) throws IOException {
            int bytesRead = inputStream.read(b, off, len);
            if (bytesRead > 0) {
                readCount += bytesRead;
            }
            return bytesRead;
        }
    }
}
