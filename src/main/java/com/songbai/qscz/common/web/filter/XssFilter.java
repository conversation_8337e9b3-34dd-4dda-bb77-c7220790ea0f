package com.songbai.qscz.common.web.filter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.songbai.qscz.common.util.SpringUtils;
import com.songbai.qscz.common.web.config.properties.XssProperties;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.HttpMethod;
import org.springframework.util.AntPathMatcher;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 防止XSS攻击的过滤器
 *
 * <AUTHOR>
 */
public class XssFilter implements Filter {
    /**
     * 排除链接
     */
    public List<String> excludes = new ArrayList<> ();

    @Override
    public void init (FilterConfig filterConfig) throws ServletException {
        XssProperties properties = SpringUtils.getBean (XssProperties.class);
        excludes.addAll (properties.getExcludeUrls ());
    }

    @Override
    public void doFilter (ServletRequest request, ServletResponse response, FilterChain chain)
        throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse resp = (HttpServletResponse) response;
        if (handleExcludeURL (req, resp)) {
            chain.doFilter (request, response);
            return;
        }
        XssHttpServletRequestWrapper xssRequest = new XssHttpServletRequestWrapper ((HttpServletRequest) request);
        chain.doFilter (xssRequest, response);
    }

    private boolean handleExcludeURL (HttpServletRequest request, HttpServletResponse response) {
        String url = request.getServletPath ();
        String method = request.getMethod ();
        // GET DELETE 不过滤
        if (method == null || HttpMethod.GET.matches (method) || HttpMethod.DELETE.matches (method)) {
            return true;
        }

        if (StrUtil.isEmpty (url) || CollUtil.isEmpty (excludes)) {
            return false;
        }
        AntPathMatcher matcher = new AntPathMatcher ();
        for (String pattern : excludes) {
            if (matcher.match (pattern, url)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void destroy () {

    }
}
