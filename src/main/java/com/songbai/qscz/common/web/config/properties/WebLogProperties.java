package com.songbai.qscz.common.web.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Web请求日志 配置属性
 *
 * <AUTHOR> Li
 */
@Data
@Component
@ConfigurationProperties(prefix = "web-log")
public class WebLogProperties {

    /**
     * 是否启用性能统计
     */
    private Boolean enablePerformance = false;

    /**
     * 慢请求阈值（毫秒），只对慢请求记录警告日志
     */
    private long slowRequestThreshold = 1500;

    /**
     * 是否记录详细参数
     */
    private Boolean enableDetailParams = false;
}
