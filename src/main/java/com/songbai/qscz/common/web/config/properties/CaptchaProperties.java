package com.songbai.qscz.common.web.config.properties;

import com.songbai.qscz.common.web.enums.CaptchaCategory;
import com.songbai.qscz.common.web.enums.CaptchaType;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 验证码 配置属性
 *
 * <AUTHOR> <PERSON>
 */
@Data
@ConfigurationProperties(prefix = "captcha")
public class CaptchaProperties {

    private Boolean enable;

    /**
     * 验证码类型
     */
    private CaptchaType type;

    /**
     * 验证码类别
     */
    private CaptchaCategory category;

    /**
     * 数字验证码位数
     */
    private Integer numberLength;

    /**
     * 字符验证码长度
     */
    private Integer charLength;


    // /**
    //  * TODO 生成图片验证码示例
    //  */
    // @GetMapping ("/auth/code")
    // public R<CaptchaVo> getCode() {
    //     boolean captchaEnabled = captchaProperties.getEnable();
    //     if (!captchaEnabled) {
    //         CaptchaVo captchaVo = new CaptchaVo();
    //         captchaVo.setCaptchaEnabled(false);
    //         return R.ok(captchaVo);
    //     }
    //     return R.ok(SpringUtils.getAopProxy(this).getCodeImpl());
    // }
    //
    // /**
    //  * 生成验证码
    //  * 独立方法避免验证码关闭之后仍然走限流
    //  */
    // @RateLimiter (time = 60, count = 10, limitType = LimitType.IP)
    // public CaptchaVo getCodeImpl() {
    //     // 保存验证码信息
    //     String uuid = IdUtil.simpleUUID();
    //     String verifyKey = GlobalConstants.CAPTCHA_CODE_KEY + uuid;
    //     // 生成验证码
    //     CaptchaType captchaType = captchaProperties.getType();
    //     boolean isMath = CaptchaType.MATH == captchaType;
    //     Integer length = isMath ? captchaProperties.getNumberLength() : captchaProperties.getCharLength();
    //     CodeGenerator codeGenerator = ReflectUtils.newInstance(captchaType.getClazz(), length);
    //     AbstractCaptcha captcha = SpringUtils.getBean(captchaProperties.getCategory().getClazz());
    //     captcha.setGenerator(codeGenerator);
    //     captcha.createCode();
    //     // 如果是数学验证码，使用SpEL表达式处理验证码结果
    //     String code = captcha.getCode();
    //     if (isMath) {
    //         ExpressionParser parser = new SpelExpressionParser ();
    //         Expression exp = parser.parseExpression(StringUtils.remove(code, "="));
    //         code = exp.getValue(String.class);
    //     }
    //     RedisUtils.setCacheObject(verifyKey, code, Duration.ofMinutes(Constants.CAPTCHA_EXPIRATION));
    //     CaptchaVo captchaVo = new CaptchaVo();
    //     captchaVo.setUuid(uuid);
    //     captchaVo.setImg(captcha.getImageBase64());
    //     return captchaVo;
    // }

}
