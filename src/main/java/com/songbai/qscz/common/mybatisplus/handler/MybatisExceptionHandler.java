package com.songbai.qscz.common.mybatisplus.handler;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import com.songbai.qscz.common.core.base.Result;
import com.songbai.qscz.common.util.ResultUtil;
import com.songbai.qscz.common.web.util.RequestParamExtractor;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.MyBatisSystemException;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.sql.SQLException;

/**
 * Mybatis异常处理器
 *
 * <AUTHOR> Li
 */
@Slf4j
@RestControllerAdvice
public class MybatisExceptionHandler {

    /**
     * 主键或 UNIQUE 索引冲突异常
     */
    @ExceptionHandler(DuplicateKeyException.class)
    public Result<Void> handleDuplicateKeyException(DuplicateKeyException e, HttpServletRequest request) {
        logExceptionDetail (request, e, "主键/唯一索引冲突异常");
        return ResultUtil.error(HttpStatus.HTTP_CONFLICT, "数据库中已存在该记录，请检查是否重复提交");
    }

    /**
     * Mybatis 系统异常（如找不到数据源等）
     */
    @ExceptionHandler(MyBatisSystemException.class)
    public Result<Void> handleMyBatisSystemException(MyBatisSystemException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        String message = e.getMessage();
        if (StrUtil.contains(message, "CannotFindDataSourceException")) {
            log.error("[MyBatis异常] 请求地址 '{}'，未找到数据源", requestURI);
            return ResultUtil.error(HttpStatus.HTTP_INTERNAL_ERROR, "系统未找到数据库连接，请联系管理员");
        }
        logExceptionDetail (request, e, "MyBatis系统异常");
        return ResultUtil.error(HttpStatus.HTTP_INTERNAL_ERROR, "数据库访问异常，请稍后再试");
    }

    /**
     * Spring DAO 异常（如事务、连接池等问题）
     */
    @ExceptionHandler(DataAccessException.class)
    public Result<Void> handleDataAccessException(DataAccessException e, HttpServletRequest request) {
        logExceptionDetail (request, e, "数据库操作异常");
        return ResultUtil.error("数据库操作异常，请联系管理员");
    }

    /**
     * JDBC 异常（如SQL语法错误、连接断开等）
     */
    @ExceptionHandler(SQLException.class)
    public Result<Void> handleSQLException(SQLException e, HttpServletRequest request) {
        logExceptionDetail (request, e, "SQL执行异常");
        return ResultUtil.error("数据库执行异常，请联系管理员");
    }

    /**
     * 打印详细异常信息
     *
     * @param request
     * @param e
     * @param title
     */
    private void logExceptionDetail (HttpServletRequest request, Exception e, String title) {
        String requestURI = request.getRequestURI ();
        log.error ("********** {} 异常开始 **********", title);
        log.error ("请求地址: {}", requestURI);
        log.error ("请求URL: {}", request.getRequestURL ());
        String paramInfo = RequestParamExtractor.extractParameters (request);
        log.error ("请求参数: {}", paramInfo);
        log.error ("异常信息: {}", e.getMessage (), e);
        for (StackTraceElement element : e.getStackTrace ()) {
            log.error ("    {}", element.toString ());
        }
        log.error ("********** {} 异常结束 **********", title);
    }
}
