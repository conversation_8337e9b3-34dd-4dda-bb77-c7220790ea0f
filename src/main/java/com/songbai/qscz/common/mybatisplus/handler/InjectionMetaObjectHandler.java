package com.songbai.qscz.common.mybatisplus.handler;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.songbai.qscz.common.core.exception.CustomServiceException;
import com.songbai.qscz.common.mybatisplus.core.domain.BaseModel;
import com.songbai.qscz.common.satoken.model.LoginUser;
import com.songbai.qscz.common.satoken.util.LoginHelper;
import com.songbai.qscz.common.util.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;

import java.util.Date;

/**
 * MP注入处理器
 *
 * <AUTHOR> Li
 * @date 2021/4/25
 */
@Slf4j
public class InjectionMetaObjectHandler implements MetaObjectHandler {

    /**
     * 如果用户不存在默认注入-1代表无用户
     */
    private static final Integer DEFAULT_USER_ID = -1;
    private static final String DEFAULT_USER_NAME = "System";

    /**
     * 插入填充方法，用于在插入数据时自动填充实体对象中的创建时间、更新时间、创建人、更新人等信息
     *
     * @param metaObject 元对象，用于获取原始对象并进行填充
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        try {
            if (ObjectUtil.isNotNull(metaObject) && metaObject.getOriginalObject() instanceof BaseModel baseEntity) {
                // 获取当前时间作为创建时间和更新时间，如果创建时间不为空，则使用创建时间，否则使用当前时间
                Date current = ObjectUtils.notNull(baseEntity.getCreateTime(), new Date());
                baseEntity.setCreateTime(current);
                baseEntity.setUpdateTime(current);

                // 如果创建人为空，则填充当前登录用户的信息
                if (ObjectUtil.isNull(baseEntity.getCreateBy())) {
                    LoginUser loginUser = getLoginUser();
                    if (ObjectUtil.isNotNull(loginUser)) {
                        String nickName = loginUser.getNickname ();
                        // 填充创建人、更新人和创建部门信息
                        baseEntity.setCreateBy(nickName);
                        baseEntity.setUpdateBy(nickName);
                    }
                } else {
                    // 填充创建人、更新人和创建部门信息
                    baseEntity.setCreateBy(DEFAULT_USER_NAME);
                    baseEntity.setUpdateBy(DEFAULT_USER_NAME);
                }
            } else {
                Date date = new Date();
                this.strictInsertFill(metaObject, "createTime", Date.class, date);
                this.strictInsertFill(metaObject, "updateTime", Date.class, date);
            }
        } catch (Exception e) {
            throw new CustomServiceException ("自动注入异常 => " + e.getMessage());
        }
    }

    /**
     * 更新填充方法，用于在更新数据时自动填充实体对象中的更新时间和更新人信息
     *
     * @param metaObject 元对象，用于获取原始对象并进行填充
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        try {
            if (ObjectUtil.isNotNull(metaObject) && metaObject.getOriginalObject() instanceof BaseModel baseEntity) {
                // 获取当前时间作为更新时间，无论原始对象中的更新时间是否为空都填充
                Date current = new Date();
                baseEntity.setUpdateTime(current);

                // 获取当前登录用户的ID，并填充更新人信息
                String nickname = LoginHelper.getLoginUser ().getNickname ();
                if (StrUtil.isNotBlank (nickname)) {
                    baseEntity.setUpdateBy(nickname);
                } else {
                    baseEntity.setUpdateBy(DEFAULT_USER_NAME);
                }
            } else {
                this.strictUpdateFill(metaObject, "updateTime", Date.class, new Date());
            }
        } catch (Exception e) {
            throw new CustomServiceException ("自动注入异常 => " + e.getMessage());
        }
    }

    /**
     * 获取当前登录用户信息
     *
     * @return 当前登录用户的信息，如果用户未登录则返回 null
     */
    private LoginUser getLoginUser() {
        LoginUser loginUser;
        try {
            loginUser = LoginHelper.getLoginUser();
        } catch (Exception e) {
            return null;
        }
        return loginUser;
    }

}
