package com.songbai.qscz.common.satoken.model;

import lombok.Data;

/**
 * 登录验证信息
 *
 * <AUTHOR>
 */
@Data
public class LoginVo {

    /**
     * 登录状态（true 成功 false 失败）
     */
    private boolean loginSuccess;

    /**
     * 登录失败信息
     */
    private String loginFailMessage;

    /**
     * 授权令牌
     */
    private String accessToken;

    /**
     * 用户 openid
     */
    private String openId;

    /**
     * 用户 unionid
     */
    private String unionId;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 机构是否开启家长数据权限(0:否,1:是)
     */
    private Integer isParent;

    /**
     * 开启家长数据权限后机构是否开启家长签课功能 1开启 0关闭
     */
    private Integer isCourseSign;

    /**
     * 青松成长用户
     */
    // TODO 放入用户信息
    // private SysParent qsczUser;


}
