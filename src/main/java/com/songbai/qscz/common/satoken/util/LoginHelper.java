package com.songbai.qscz.common.satoken.util;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.songbai.qscz.common.core.enums.UserType;
import com.songbai.qscz.common.satoken.model.LoginUser;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;


/**
 * 登录鉴权助手
 * <p>
 * user_type 为 用户类型 同一个用户表 可以有多种用户类型 例如 pc,app
 * deivce 为 设备类型 同一个用户类型 可以有 多种设备类型 例如 web,ios
 * 可以组成 用户类型与设备类型多对多的 权限灵活控制
 * <p>
 * 多用户体系 针对 多种用户类型 但权限控制不一致
 * 可以组成 多用户类型表与多设备类型 分别控制权限
 *
 * <AUTHOR> Li
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class LoginHelper {

    public static final String LOGIN_USER_KEY = "loginUser";
    public static final String QSCZ_USER_ID_KEY = "qsczUserId";
    public static final String ERP_USER_ID_KEY = "erpUserId";
    public static final String USER_CENTER_USER_ID_KEY = "userCenterUserId";
    public static final String QSCZ_USER_KEY = "qsczUser";
    public static final String ERP_USER_KEY = "erpUser";
    public static final String USER_CENTER_USER_KEY = "userCenterUser";
    public static final String USER_NAME_KEY = "userName";



    public static void setLoginUser(LoginUser loginUser) {
        SaSession session = StpUtil.getTokenSession();
        if (ObjectUtil.isNotNull(session)) {
            session.set(LOGIN_USER_KEY, loginUser);
        }
    }

    /**
     * 获取用户(多级缓存)
     */
    @SuppressWarnings("unchecked cast")
    public static <T extends LoginUser> T getLoginUser() {
        SaSession session = StpUtil.getTokenSession();
        if (ObjectUtil.isNull(session)) {
            return null;
        }
        return (T) session.get(LOGIN_USER_KEY);
    }

    // TODO 放入用户信息
    // public static void setQsczUser(SysParent qsczUser) {
    //     SaSession session = StpUtil.getTokenSession();
    //     if (ObjectUtil.isNotNull(session)) {
    //         session.set(QSCZ_USER_KEY, qsczUser);
    //     }
    // }
    //
    //
    // /**
    //  * 获取用户(多级缓存)
    //  */
    // @SuppressWarnings("unchecked cast")
    // public static <T extends SysParent> T getQSCZUser() {
    //     SaSession session = StpUtil.getTokenSession();
    //     if (ObjectUtil.isNull(session)) {
    //         return null;
    //     }
    //     return (T) session.get(QSCZ_USER_KEY);
    // }

    /**
     * 获取用户基于token
     */
    @SuppressWarnings("unchecked cast")
    public static <T extends LoginUser> T getLoginUser(String token) {
        SaSession session = StpUtil.getTokenSessionByToken(token);
        if (ObjectUtil.isNull(session)) {
            return null;
        }
        return (T) session.get(LOGIN_USER_KEY);
    }

    /**
     * 获取青松成长用户id
     */
    public static Integer getQsczUserId() {
        return Convert.toInt (getExtra(QSCZ_USER_ID_KEY));
    }

    /**
     * 获取ERP用户ID
     * @return
     */
    public static Integer getErpUserId() {
        return isLogin()?getLoginUser().getErpUserId ():null;
    }

    /**
     * 获取用户账户
     */
    public static String getUsername() {
        return Convert.toStr(getExtra(USER_NAME_KEY));
    }

    /**
     * 获取用户类型
     */
    public static String getUserType() {
        return isLogin()?getLoginUser().getUserType ():null;
    }

    /**
     * 是否为ERP用户
     * @return
     */
    public static boolean isErp() {
        return UserType.ERP_USER.getUserType ().equals (getUserType ());
    }

    /**
     * 获取当前 Token 的扩展信息
     *
     * @param key 键值
     * @return 对应的扩展数据
     */
    private static Object getExtra(String key) {
        try {
            return StpUtil.getExtra(key);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 检查当前用户是否已登录
     *
     * @return 结果
     */
    public static boolean isLogin() {
        try {
            return getLoginUser() != null;
        } catch (Exception e) {
            return false;
        }
    }

}
