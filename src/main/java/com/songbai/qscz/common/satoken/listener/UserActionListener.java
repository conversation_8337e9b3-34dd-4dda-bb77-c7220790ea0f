// package com.songbai.qscz.common.satoken.listener;
//
// import cn.dev33.satoken.annotation.handler.SaAnnotationHandlerInterface;
// import cn.dev33.satoken.config.SaTokenConfig;
// import cn.dev33.satoken.listener.SaTokenListener;
// import cn.dev33.satoken.stp.StpLogic;
// import cn.dev33.satoken.stp.StpUtil;
// import cn.dev33.satoken.stp.parameter.SaLoginParameter;
// import cn.hutool.core.convert.Convert;
// import cn.hutool.http.useragent.UserAgent;
// import cn.hutool.http.useragent.UserAgentUtil;
// import com.songbai.qscz.common.log.event.LoginInfoEvent;
// import com.songbai.qscz.common.util.ServletUtils;
// import com.songbai.qscz.common.util.SpringUtils;
// import lombok.RequiredArgsConstructor;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.stereotype.Component;
//
// import java.time.Duration;
//
// /**
//  * TODO 用户行为 侦听器的实现
//  *
//  * <AUTHOR> Li
//  */
// @RequiredArgsConstructor
// @Component
// @Slf4j
// public class UserActionListener implements SaTokenListener {
//
//     private final SysLoginService loginService;
//
//     /**
//      * 每次登录时触发
//      */
//     @Override
//     public void doLogin(String loginType, Object loginId, String tokenValue, SaLoginParameter loginParameter) {
//         UserAgent userAgent = UserAgentUtil.parse(ServletUtils.getRequest().getHeader("User-Agent"));
//         String ip = ServletUtils.getClientIP();
//         UserOnlineDTO dto = new UserOnlineDTO();
//         dto.setIpaddr(ip);
//         dto.setLoginLocation(AddressUtils.getRealAddressByIP(ip));
//         dto.setBrowser(userAgent.getBrowser().getName());
//         dto.setOs(userAgent.getOs().getName());
//         dto.setLoginTime(System.currentTimeMillis());
//         dto.setTokenId(tokenValue);
//         String username = (String) loginParameter.getExtra(LoginHelper.USER_NAME_KEY);
//         String tenantId = (String) loginParameter.getExtra(LoginHelper.TENANT_KEY);
//         dto.setUserName(username);
//         dto.setClientKey((String) loginParameter.getExtra(LoginHelper.CLIENT_KEY));
//         dto.setDeviceType(loginParameter.getDeviceType());
//         dto.setDeptName((String) loginParameter.getExtra(LoginHelper.DEPT_NAME_KEY));
//         TenantHelper.dynamic(tenantId, () -> {
//             if(loginParameter.getTimeout() == -1) {
//                 RedisUtils.setCacheObject(CacheConstants.ONLINE_TOKEN_KEY + tokenValue, dto);
//             } else {
//                 RedisUtils.setCacheObject(CacheConstants.ONLINE_TOKEN_KEY + tokenValue, dto, Duration.ofSeconds(loginParameter.getTimeout()));
//             }
//         });
//         // 记录登录日志
//         LoginInfoEvent logininforEvent = new LoginInfoEvent ();
//         logininforEvent.setStatus(Constants.LOGIN_SUCCESS);
//         logininforEvent.setMessage(MessageUtils.message("user.login.success"));
//         logininforEvent.setRequest(ServletUtils.getRequest());
//         SpringUtils.context().publishEvent(logininforEvent);
//         // 更新登录信息
//         loginService.recordLoginInfo((Long) loginParameter.getExtra(LoginHelper.USER_KEY), ip);
//         log.info("user doLogin, userId:{}, token:{}", loginId, tokenValue);
//     }
//
//     /**
//      * 每次注销时触发
//      */
//     @Override
//     public void doLogout(String loginType, Object loginId, String tokenValue) {
//         String tenantId = Convert.toStr(StpUtil.getExtra(tokenValue, LoginHelper.TENANT_KEY));
//         TenantHelper.dynamic(tenantId, () -> {
//             RedisUtils.deleteObject(CacheConstants.ONLINE_TOKEN_KEY + tokenValue);
//         });
//         log.info("user doLogout, userId:{}, token:{}", loginId, tokenValue);
//     }
//
//     /**
//      * 每次被踢下线时触发
//      */
//     @Override
//     public void doKickout(String loginType, Object loginId, String tokenValue) {
//         String tenantId = Convert.toStr(StpUtil.getExtra(tokenValue, LoginHelper.TENANT_KEY));
//         TenantHelper.dynamic(tenantId, () -> {
//             RedisUtils.deleteObject(CacheConstants.ONLINE_TOKEN_KEY + tokenValue);
//         });
//         log.info("user doKickout, userId:{}, token:{}", loginId, tokenValue);
//     }
//
//     /**
//      * 每次被顶下线时触发
//      */
//     @Override
//     public void doReplaced(String loginType, Object loginId, String tokenValue) {
//         String tenantId = Convert.toStr(StpUtil.getExtra(tokenValue, LoginHelper.TENANT_KEY));
//         TenantHelper.dynamic(tenantId, () -> {
//             RedisUtils.deleteObject(CacheConstants.ONLINE_TOKEN_KEY + tokenValue);
//         });
//         log.info("user doReplaced, userId:{}, token:{}", loginId, tokenValue);
//     }
//
//     /**
//      * 每次被封禁时触发
//      */
//     @Override
//     public void doDisable(String loginType, Object loginId, String service, int level, long disableTime) {
//     }
//
//     /**
//      * 每次被解封时触发
//      */
//     @Override
//     public void doUntieDisable(String loginType, Object loginId, String service) {
//     }
//
//     /**
//      * 每次打开二级认证时触发
//      */
//     @Override
//     public void doOpenSafe(String loginType, String tokenValue, String service, long safeTime) {
//     }
//
//     /**
//      * 每次创建Session时触发
//      */
//     @Override
//     public void doCloseSafe(String loginType, String tokenValue, String service) {
//     }
//
//     /**
//      * 每次创建Session时触发
//      */
//     @Override
//     public void doCreateSession(String id) {
//     }
//
//     /**
//      * 每次注销Session时触发
//      */
//     @Override
//     public void doLogoutSession(String id) {
//     }
//
//     /**
//      * 每次 Token 续期时触发（注意：是 timeout 续期，而不是 active-timeout 续期）
//      *
//      * @param loginType  账号类别
//      * @param loginId    账号id
//      * @param tokenValue token 值
//      * @param timeout    续期时间
//      */
//     @Override
//     public void doRenewTimeout (String loginType, Object loginId, String tokenValue, long timeout) {
//
//     }
//
//     /**
//      * 全局组件载入
//      *
//      * @param compName 组件名称
//      * @param compObj  组件对象
//      */
//     @Override
//     public void doRegisterComponent (String compName, Object compObj) {
//         SaTokenListener.super.doRegisterComponent (compName, compObj);
//     }
//
//     /**
//      * 注册了自定义注解处理器
//      *
//      * @param handler 注解处理器
//      */
//     @Override
//     public void doRegisterAnnotationHandler (SaAnnotationHandlerInterface<?> handler) {
//         SaTokenListener.super.doRegisterAnnotationHandler (handler);
//     }
//
//     /**
//      * StpLogic 对象替换
//      *
//      * @param stpLogic /
//      */
//     @Override
//     public void doSetStpLogic (StpLogic stpLogic) {
//         SaTokenListener.super.doSetStpLogic (stpLogic);
//     }
//
//     /**
//      * 载入全局配置
//      *
//      * @param config /
//      */
//     @Override
//     public void doSetConfig (SaTokenConfig config) {
//         SaTokenListener.super.doSetConfig (config);
//     }
//
// }
